{"name": "crm-official-website", "version": "0.0.0", "private": true, "type": "module", "scripts": {"start": "npm run serve", "serve": "npm run serve-china", "serve-china": "vite  --mode  development.china", "serve-other": "vite  --mode  development.otherCountries", "build-only": "vite build", "build": "npm run build-prod && npm run build-staging", "build-all": "npm run build-prod-all && npm run build-staging-all", "build-staging-all": "npm run build-staging-china && npm run build-staging-other", "build-prod-all": "npm run build-prod-china && npm run build-prod-other", "build-staging-china": "vite build --mode staging.china", "build-staging-other": "vite build --mode staging.otherCountries", "build-prod-china": "vite build --mode production.china", "build-prod-other": "vite build --mode production.otherCountries", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "i18n:extract": "vue-i18n-extract report --vueFiles './src/**/*.?(js|vue)' --languageFiles './src/i18n/lang/{zh,en,ja}.json' > i18n-report.txt", "i18n:remove-unused": "vue-i18n-extract --remove --vueFiles './src/**/*.?(js|vue)' --languageFiles './src/i18n/lang/{zh,en,ja}.json'"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.7", "element-plus": "^2.9.1", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "swiper": "^11.1.5", "vue": "^3.4.15", "vue-gtag-next": "^1.14.0", "vue-i18n": "^9.9.1", "vue-router": "^4.2.5"}, "devDependencies": {"@iconify-json/fluent-mdl2": "^1.2.1", "@playwright/test": "^1.54.2", "@rushstack/eslint-patch": "^1.3.3", "@unocss/eslint-config": "^0.63.4", "@unocss/transformer-directives": "^0.63.4", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.2.0", "less-loader": "^12.2.0", "npm-run-all": "^4.1.5", "prettier": "^3.0.3", "unocss": "^0.63.4", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.0.11", "vue-i18n-extract": "^2.0.7"}, "config": {"commitizen": {"path": "cz-git"}}}