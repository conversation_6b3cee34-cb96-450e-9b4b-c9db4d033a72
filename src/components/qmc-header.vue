<template>
  <header :class="['fixed box-border top-0 z-99 h-90px w-full bg-#221C02  text-base']">
    <div class="mx-auto h-full w-1200px flex justify-between">
      <div class="flex gap-50px">
        <a class="flex items-center" href="#">
          <img :src="logo" class="h-46px w-244px" />
        </a>
        <!-- <nav class="relative h-full flex items-center gap-4"> -->
        <!--   <a -->
        <!--     :class="[ -->
        <!--       'relative flex cursor-pointer items-center px-30px text-xl  text-#FFF3BD font-400', -->
        <!--       'hover:(opacity-70 text-shadow-[0_0_2px_rgba(79,62,62,0.3)])', -->
        <!--       { -->
        <!--         '!font-300': commonStore.currentLocale === 'en', -->
        <!--       }, -->
        <!--     ]" -->
        <!--     href="javascript:void(0);" -->
        <!--     @click="handleRouterClick('home')" -->
        <!--   > -->
        <!--     {{ $t('主页') }} -->
        <!--   </a> -->
        <!--   <a -->
        <!--     :class="[ -->
        <!--       'relative flex cursor-pointer items-center px-30px text-xl  text-#FFF3BD font-400', -->
        <!--       'hover:(opacity-70 text-shadow-[0_0_2px_rgba(79,62,62,0.3)])', -->
        <!--       { -->
        <!--         '!font-300': commonStore.currentLocale === 'en', -->
        <!--       }, -->
        <!--     ]" -->
        <!--     href="javascript:void(0);" -->
        <!--     @click="handleRouterClick('price')" -->
        <!--   > -->
        <!--     {{ $t('订购') }} -->
        <!--   </a> -->
        <!-- </nav> -->
      </div>
      <nav class="flex items-center">
        <selectLocale class="!text-#7F8285"></selectLocale>
        <div class="mx-40px h-18px w-1px bg-#4A421F"></div>
        <div class="min-w-190px flex gap-20px">
          <div
            class="h-33px w-84px cursor-pointer rounded-6px bg-#666544 text-center color-#FFF3BD line-height-33px"
            @click="handleRouterClick('price')"
          >
            {{ $t('注册') }}
          </div>
          <div
            class="h-33px w-84px cursor-pointer rounded-6px bg-#DEDD9D text-center line-height-33px"
          >
            <el-dropdown :teleporte="false" @command="handleLogin" class="cursor-pointer px-2">
              <div class="text-16px color-#221C02 line-height-33px">
                {{ $t('登录') }}
              </div>
              <span class="flex items-center whitespace-nowrap text-base"></span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="olive">
                    {{ $t('EduRP橄榄版') }}
                  </el-dropdown-item>
                  <el-dropdown-item command="green">
                    {{ $t('EduRP智校版') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </nav>
    </div>
  </header>
</template>
<script setup>
  import { inject, computed, ref } from 'vue';
  import selectLocale from '@/components/select-locale';
  import { useRoute, useRouter } from 'vue-router';
  import logo from '@/assets/images/new-logo.png';
  import { useCommonStore } from '@/stores/common';

  const commonStore = useCommonStore();
  const { updateClickNavName } = inject('navName');

  const routerObj = {
    home: 'home',
    price: 'price',
    sign: 'sign',
  };

  const route = useRoute();
  const activeName = computed(() => route.name);
  const showSignupBtn = computed(() => {
    return activeName.value !== 'price';
  });

  const router = useRouter();
  const handleRouterClick = (value) => {
    if (value === 'sign') {
      console.log('跳转到注册页面');
    } else {
      router.push({
        name: routerObj[value],
      });
      updateClickNavName(routerObj[value]);
    }
  };
  const urlObj = {
    green: `${import.meta.env.VITE_GREEN_URL}`,
    olive: `${import.meta.env.VITE_OLIVE_URL}`,
  };
  const handleLogin = (value) => {
    location.href = `${urlObj[value]}/login`;
  };
</script>
<style lang="less" scoped>
  // .yj-el-dropdown-item {
  //   background: #000 !important;
  // }
</style>
