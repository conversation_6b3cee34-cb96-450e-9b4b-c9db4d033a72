<template>
  <div class="form-dialog">
    <el-dialog
      class="dialog"
      :title="$t('messageBoard')"
      v-model="isDialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="contactForm" label-width="auto">
        <!-- 名字输入 -->
        <el-form-item :label="$t('name')" prop="name">
          <el-input v-model="form.name" :placeholder="$t('name')"></el-input>
        </el-form-item>

        <!-- 邮箱输入 -->
        <el-form-item :label="$t('email')" prop="email">
          <el-input v-model="form.email" :placeholder="$t('email')"></el-input>
        </el-form-item>

        <!-- 留言输入 -->
        <el-form-item :label="$t('message')" prop="message">
          <el-input
            v-model="form.message"
            type="textarea"
            :rows="4"
            :placeholder="$t('message')"
          ></el-input>
        </el-form-item>

        <!-- style="background-color: var(--pigment-indigo)" -->
        <!-- 提交按钮 -->
        <div class="w-full flex justify-center">
          <el-button class="default-btn" type="primary" @click="submitForm">
            {{ $t('submit') }}
          </el-button>
          <el-button class="default-btn" @click="resetForm">{{ $t('reset') }}</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, defineEmits } from 'vue';
  import APICustomer from '@/service/APIcustomer.js';
  import { ElMessage } from 'element-plus';
  import i18n from '@/i18n';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  });

  console.log(props.modelValue);
  // 通知父组件更新 modelValue
  const emit = defineEmits(['update:modelValue']);
  // 计算属性，用于双向绑定
  const isDialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 表单数据
  const form = ref({
    name: '',
    email: '',
    message: '',
  });

  // 表单验证规则
  const rules = ref({
    name: [{ required: true, message: i18n.t('validation.nameRequired'), trigger: 'blur' }],
    email: [
      { required: true, message: i18n.t('validation.emailRequired'), trigger: 'blur' },
      { type: 'email', message: i18n.t('validation.emailInvalid'), trigger: 'blur' },
    ],
    message: [{ required: true, message: i18n.t('validation.messageRequired'), trigger: 'blur' }],
  });

  // 表单引用
  const contactForm = ref(null);

  // 提交表单
  const submitForm = () => {
    contactForm.value.validate(async (valid) => {
      if (valid) {
        console.log('表单数据:', form.value);
        try {
          const result = await APICustomer.submitInquiryAPI({
            data: {
              inquirerName: form.value.name,
              inquirerEmail: form.value.email,
              message: form.value.message,
            },
          });
          if (!result?.body) throw new Error();

          ElMessage({
            type: 'success',
            message: i18n.t('邮件发送成功。'),
          });

          isDialogVisible.value = false;
        } catch (error) {
          console.log('error----', error);
          ElMessage({
            type: 'error',
            message: i18n.t('邮件发送失败，请记录下邮箱地址后手动发送邮件。'),
            duration: 6000,
          });
        }
      } else {
        return false;
      }
    });
  };

  // 重置表单
  const resetForm = () => {
    contactForm.value.resetFields();
  };
</script>

<style scoped>
  .el-form {
    max-width: 600px;
    margin: 0 auto;
  }

  :deep(.el-dialog__headerbtn:hover .el-dialog__close) {
    color: var(--pigment-indigo);
  }
</style>
