<template>
  <div class="password-input-with-strength">
    <el-form-item :label="label" :prop="propName" :rules="passwordRules" :label-width="labelWidth">
      <el-input
        type="password"
        v-model="myForm.password"
        :placeholder="placeholder"
        :show-password="true"
        autocomplete="off"
        @input="checkPasswordStrength"
      ></el-input>
    </el-form-item>

    <div class="strength-container">
      <p class="strength-title">
        {{ $t('密码强度') }}:
        <span :class="passwordStrength.className">
          {{ passwordStrength.text }}
        </span>
      </p>
      <div class="strength-item">
        <span :class="{ 'strength-dot': true, valid: strength.length }"></span>
        <span>{{ $t('stre1') }}</span>
      </div>
      <div class="strength-item">
        <span :class="{ 'strength-dot': true, valid: strength.number }"></span>
        <span>{{ $t('stre2') }}</span>
      </div>
      <div class="strength-item">
        <span :class="{ 'strength-dot': true, valid: strength.upper }"></span>
        <span>{{ $t('stre3') }}</span>
      </div>
      <div class="strength-item">
        <span :class="{ 'strength-dot': true, valid: strength.special }"></span>
        <span>{{ $t('stre4') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import i18n from '@/i18n';
  // TODO 本组件会修改父组件传入的form对象的propName的值,需要注意
  const props = defineProps({
    label: {
      type: String,
      default: i18n.t('创建密码'),
    },
    placeholder: {
      type: String,
      default: i18n.t('请输入'),
    },
    labelWidth: {
      type: String,
      default: '80px',
    },
    propName: {
      type: String,
      required: true,
    },
    form: {
      type: Object,
      required: true,
    },
  });

  const strength = reactive({
    length: false,
    upper: false,
    number: false,
    special: false,
  });

  const myForm = reactive(props.form);
  const strengthMinimun = ref(false);

  const REG_LENGTH = (password) => /.{8,}/.test(password);
  const REG_NUMBER = (password) => /[0-9]/.test(password) && /[~!#$%^&*_+]/.test(password);
  const REG_UPPER = (password) => /[A-Z]/.test(password) && /[a-z]/.test(password);
  const REG_SPECIAL = (password) => !/[\s]/.test(password) && !/[^\w~!#$%^&*_+]/.test(password);
  const REG_MINIMUM = (password) => /^(?=.*[a-zA-Z])(?=.*\d)[^]{8,16}$/.test(password);

  const passwordStrength = computed(() => {
    let result = { className: '', text: '' };
    const values = Object.values(strength);
    const trueLength = values.filter((v) => v).length;
    if (trueLength == 4) {
      result = { className: 'good', text: i18n.t('强') };
    } else if (trueLength >= 1) {
      result = { className: 'weak', text: i18n.t('弱') };
    }
    return result;
  });

  const passwordRules = computed(() => {
    return [
      {
        required: true,
        message: i18n.t('请输入密码'),
        trigger: ['change', 'blur'],
      },
      {
        validator: (rule, value, callback) => {
          if (!strengthMinimun.value) {
            callback(new Error(i18n.t('密码至少8位，至少1个字母和1个数字')));
          } else if (!strength.special) {
            callback(new Error(i18n.t('stre4')));
          } else {
            callback();
          }
        },
        trigger: ['change', 'blur'],
      },
    ];
  });

  const checkPasswordStrength = (value) => {
    const passwordValue = myForm.password;
    strength.length = REG_LENGTH(passwordValue);
    strength.number = REG_NUMBER(passwordValue);
    strength.upper = REG_UPPER(passwordValue);
    strength.special = REG_SPECIAL(passwordValue);
    strengthMinimun.value = REG_MINIMUM(passwordValue);
  };
</script>

<style lang="less" scoped>
  @green: #58c0ad;
  @red: #e36560;
  .strength-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    align-items: center;
    padding: 0 20px;
    margin: 10px 0 10px 0;
    .strength-title {
      color: #8e8f91;
    }
    .strength-item {
      color: #8e8f91;
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  .weak {
    color: @red;
  }
  .good {
    color: @green;
  }
  .strength-dot {
    flex-shrink: 0;
    width: 5px;
    height: 5px;
    background-color: @red;
    border-radius: 50%;
  }
  .strength-dot.valid {
    background-color: @green;
  }
</style>
