<template>
  <div class="bg-view min-w-950px">
    <div class="bg-content">
      <!-- <div class="select-locale-x"> -->
      <!--   <selectLocale class="select-locale"></selectLocale> -->
      <!-- </div> -->
      <div class="flex flex-col items-center">
        <img class="mb-36px w-366px" src="@/assets/images/new-logo.png" alt="" />
        <div class="box">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import selectLocale from '@/components/select-locale';
</script>

<style lang="less" scoped>
  .bg-view {
    background-color: #221c02;
    height: 100vh;
    position: relative;
    box-sizing: box-border;
    padding-top: 130px;
  }
  .bg-content {
    position: relative;
    z-index: 1;
    height: 100%;
  }
  .select-locale-x {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    height: 80px;
    padding-right: 20px;
    .select-locale {
      color: var(--sign-color-white);
      font-size: 18px;
    }
  }
  .box {
    display: flex; /* 启用 Flex 布局 */
    flex-direction: column; /* 确保子元素垂直排列 */
    width: 950px;
    min-height: 600px;
    box-sizing: border-box;
    padding: 65px;
    background: #eaebd0;
    border-radius: 14px;
  }
  .bottom-text {
    position: absolute;
    bottom: -60px;
    left: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--sign-color-gray);

    &::before {
      margin-right: 15px;
    }
    &::after {
      margin-left: 15px;
    }
    &::before,
    &::after {
      display: inline-block;
      content: '';
      width: 40px;
      height: 1px;
      background: var(--sign-color-gray);
    }
  }
</style>
