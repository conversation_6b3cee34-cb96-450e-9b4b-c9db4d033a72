<template>
  <div class="video-thumbnail-list relative z-3 flex justify-center gap-108px">
    <div v-for="(video, index) in displayedVideos" :key="index" @click="openVideo(video)">
      <div class="video-thumbnail relative h-309px w-550rpx cursor-pointer">
        <img
          :src="playIcon"
          class="absolute bottom-0 left-0 right-0 top-0 z-2 m-auto h-56px w-56px"
        />
        <img
          :src="video.thumbnail"
          alt="Video Thumbnail"
          class="h-full w-full rounded-lg brightness-80"
        />
      </div>

      <p class="mt-10px text-center text-22px leading-45px">
        {{ index === 0 ? $t('edurp介绍一') : $t('edurp介绍二') }}
      </p>
    </div>

    <!-- 视频播放弹窗 -->
    <el-dialog v-model="selectedVideo" @click="closeVideo" width="80%">
      <div class="relative mt-10px w-full" style="padding-bottom: 56.25%">
        <iframe
          :src="selectedVideo.embedUrl"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowfullscreen
          class="absolute left-0 top-0 h-full w-full rounded-md"
        ></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import i18nIn from '@/i18n';
  import { reactive, ref, computed, onMounted } from 'vue';
  import thumbnail1 from '@/assets/images/thumbnail1.jpg';
  import thumbnail2 from '@/assets/images/thumbnail2.jpg';
  import playIcon from '@/assets/images/play.png';
  const allVideos = reactive({
    bilibili: {
      video1: [
        {
          lang: 'zh',
          embedUrl:
            '//player.bilibili.com/player.html?isOutside=true&aid=114596069248857&bvid=BV1Fy7nzRE9w&cid=30228415759&p=1',
          thumbnail: thumbnail1,
        },
      ],
      video2: [
        {
          lang: 'zh',
          embedUrl:
            '//player.bilibili.com/player.html?isOutside=true&aid=114596069310814&bvid=BV1Hy7nzREMu&cid=30228941677&p=1',
          thumbnail: thumbnail2,
        },
      ],
    },
    youtube: {
      video1: [
        {
          lang: 'zh',
          embedUrl: 'https://www.youtube.com/embed/TzUCeHtv3Lg?si=9GN7LGbfQNY0aBAf',

          thumbnail: thumbnail1,
        },
        {
          lang: 'ja',
          embedUrl: 'https://www.youtube.com/embed/GjkKBgri2eg?si=o-7zOoOJR8gbRNol',
          thumbnail: thumbnail1,
        },
        {
          lang: 'en',
          embedUrl: 'https://www.youtube.com/embed/l9RaIp2VP3s?si=7y93uifoNQwTx6yx',
          thumbnail: thumbnail1,
        },
      ],
      video2: [
        {
          lang: 'zh',
          embedUrl: 'https://www.youtube.com/embed/I0jFYoRSjY8?si=MtbFey5Gq4wIvoEt',
          thumbnail: thumbnail2,
        },
        {
          lang: 'ja',
          embedUrl: 'https://www.youtube.com/embed/VPiIMi6Ky3U?si=hh-JHiy527VX2MYP',
          thumbnail: thumbnail2,
        },
        {
          lang: 'en',
          embedUrl: 'https://www.youtube.com/embed/DVj9EVjqhW8?si=3hfYQHud3rOYhkTq',
          thumbnail: thumbnail2,
        },
      ],
    },
  });

  const selectedVideo = ref(null);

  const userLang = i18nIn.getLocale();
  const langKey = userLang.startsWith('zh') ? 'zh' : userLang.startsWith('ja') ? 'ja' : 'en';

  const isInChina = ref(false);

  // 获取地区信息
  onMounted(async () => {
    try {
      const res = await fetch('https://ipapi.co/json/');
      const data = await res.json();
      isInChina.value = data?.country_code === 'CN';
    } catch (err) {
      console.error('地区判断失败，使用默认英文视频。', err);
    }
  });

  const displayedVideos = computed(() => {
    const videoType = isInChina.value ? 'bilibili' : 'youtube';

    return Object.values(allVideos[videoType])
      .map((item) => {
        let result = [];
        if (isInChina.value) {
          result = item.filter((v) => v.lang === 'zh');
        } else {
          result = item.filter((v) => v.lang === langKey);
        }

        return result;
      })
      .flat();
  });

  const openVideo = (video) => {
    selectedVideo.value = video;
  };

  const closeVideo = () => {
    selectedVideo.value = null;
  };
</script>

<style scoped>
  .video-thumbnail {
    transition: transform 0.2s;
  }

  .video-thumbnail:hover {
    transform: scale(1.02);
  }
</style>
