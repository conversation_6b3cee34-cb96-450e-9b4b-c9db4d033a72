<template>
  <div class="fixed left-0 top-1/3 z-3 hidden" :class="{ '!block': showTools }">
    <signupBtn
      url="#/price"
      class="box-border h-28.5 min-w-28.5 origin-l scale-x-100 justify-center whitespace-nowrap text-center text-lg shadow-md transition-all !flex-col !rounded-l-0 !rounded-r-lg !p-3 !hover:scale-x-105"
    >
      <span class="text-10">
        <i class="bg-icon bg-icon-arrow mb-2"></i>
      </span>
      {{ $t('订购') }}
    </signupBtn>
  </div>
</template>
<script setup>
  import signupBtn from '@/components/home/<USER>';
  import { ref, onMounted, onUnmounted } from 'vue';

  const showTools = ref(false);

  const handleScroll = () => {
    const innterHeight = window.innerHeight; // 视口高度
    const scrollTop = document.documentElement.scrollTop; // 滚动距离
    showTools.value = scrollTop >= innterHeight / 3;
  };
  onMounted(() => {
    window.addEventListener('scroll', handleScroll, false);
  });
  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll, false);
  });
</script>
<style lang="less" scoped></style>
