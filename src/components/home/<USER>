<template>
  <div class="pt-14">
    <div class="min-w-5xl">
      <div>
        <p class="mb-6 text-center text-4xl">{{ title1 }}</p>
        <p class="mx-auto text-center text-lg text-#5b5e63 leading-6">{{ title2 }}</p>
      </div>
      <div class="mx-auto pt-10">
        <div class="">
          <div class="flex items-center justify-center gap-6">
            <div
              class="box-border h-15.5 min-w-54 flex cursor-pointer select-none items-center justify-center rounded-full px-5 text-5.5"
              :class="{
                'bg-#E1DEFF text-#4E40E6': tabIndex === index,
                'text-#666F7F': tabIndex !== index,
              }"
              @click="handleTabClick(index)"
              v-for="(item, index) in tableDataList"
              :key="index"
            >
              <i
                class="bg-icon mr-2"
                :class="{
                  ['bg-icon-' + (index + 1)]: tabIndex !== index,
                  ['bg-icon-' + (index + 1) + '-dark']: tabIndex === index,
                }"
              ></i>
              {{ item.title }}
            </div>
          </div>
          <div class="mt-12">
            <div
              class="mx-auto h177.5 max-w-325 flex gap-2 overflow-hidden rounded-xl bg-#f6f8fe"
              :class="[tabLayout === 'left' ? 'flex' : 'flex-row-reverse']"
            >
              <div
                class="box-border h-full min-w-87.5 flex items-center p-3"
                :class="[tabLayout === 'left' ? 'pl-12.5' : 'pl-17.5']"
              >
                <div>
                  <div
                    v-for="(desItem, desIndex) in tabData.descriptions"
                    :key="desIndex"
                    class="mt-4 flex items-center gap-2 leading-10"
                  >
                    <div class="relative h-3 w-3 rounded-full bg-indigo">
                      <i
                        v-if="desIndex < tabData.descriptions.length - 1"
                        class="absolute left-5px top-3 h-14 w-0.5 bg-#4E40E6"
                      ></i>
                    </div>
                    <div>
                      {{ desItem }}
                    </div>
                  </div>
                  <signupBtn
                    :product="tabData.product"
                    url="#/price"
                    class="mt-10 text-22px !rounded-lg !px-7.5 !py-4"
                  ></signupBtn>
                </div>
              </div>
              <div class="h-full flex-1 flex-shrink-0 overflow-hidden">
                <!-- :pagination="{ clickable: true }" -->
                <swiper
                  :modules="modules"
                  :autoplay="{
                    delay: 2500,
                    disableOnInteraction: false,
                  }"
                  @swiper="setSwiper"
                >
                  <swiper-slide v-for="(img, index) in tabData.images" :key="index">
                    <div
                      class="mr-15 box-border h-full flex items-center justify-center py-0 pl-1"
                      :class="[{ 'pl-12.5 mr-2.5': tabLayout === 'right' }]"
                    >
                      <div class="img-box overflow-hidden rounded-lg">
                        <div class="h-10.5 flex items-center gap-5 bg-#f7f9fe pl-5">
                          <div class="h-3 w-3 rounded-full bg-#eb6a5f"></div>
                          <div class="h-3 w-3 rounded-full bg-#f4bd4f"></div>
                          <div class="h-3 w-3 rounded-full bg-#61c154"></div>
                        </div>
                        <img :src="img" class="block w-full object-contain" />
                      </div>
                    </div>
                  </swiper-slide>
                </swiper>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import signupBtn from '@/components/home/<USER>';
  import { ref, computed, watch, nextTick } from 'vue';
  // 引入swiper
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Autoplay } from 'swiper/modules';

  import 'swiper/less';
  import 'swiper/less/pagination';
  import 'swiper/less/autoplay';

  const props = defineProps({
    tabLayout: {
      type: String,
      default: 'left',
    },
    title1: {
      type: String,
      require: true,
    },
    title2: {
      type: String,
      require: true,
    },
    tabs: {
      type: Array,
      require: true,
    },
  });

  // 配置swiper
  const modules = [Autoplay];

  /**
   * 使用 swiper 组件的自定义 hook
   *
   * @param tabData swiper 组件的 tab 数据
   * @returns 包含 swiper 实例和设置 swiper 实例方法的对象
   */
  const useSwiper = (tabData) => {
    /**
     *  问题:在swiper组件上使用ref="mySwiper"会导致获取的swiper实例上的所有方法都是undefined,
     *  使用swiper组件的@swiper获取,获取的实例可以调用方法
     */

    const mySwiper = ref(null);
    /**
     * 设置 swiper 对象, 比ref获取更方便
     * @param swiper swiper 对象
     */
    const setSwiper = (swiper) => {
      mySwiper.value = swiper;
    };

    watch(tabData, async () => {
      await nextTick();
      const swiper = mySwiper.value;
      swiper?.update?.();
      swiper?.slideTo?.(0, 0);
      swiper?.autoplay?.resume?.();
    });
    return {
      mySwiper,
      setSwiper,
    };
  };
  const useTab = (tabs) => {
    const tabIndex = ref(0);
    const tableDataList = ref(tabs);

    const handleTabClick = (n) => {
      tabIndex.value = n;
    };
    const tabData = computed(() => tableDataList.value[tabIndex.value] || []);
    return {
      handleTabClick,
      tabData,
      tableDataList,
      tabIndex,
    };
  };

  const { handleTabClick, tabData, tableDataList, tabIndex } = useTab(props.tabs);
  const { setSwiper } = useSwiper(tabData);
</script>
<style lang="less" scoped>
  .swiper {
    height: 100%;
  }

  .img-box {
    box-shadow: 0 0 6px rgb(0 0 0 / 0.1);
  }
  ::v-deep {
    .swiper-pagination {
      span {
        @apply bg-indigo;
      }
    }
    .swiper-horizontal > .swiper-pagination-bullets,
    .swiper-pagination-bullets.swiper-pagination-horizontal {
      bottom: 0px;
    }
  }
</style>
