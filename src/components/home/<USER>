<template>
  <a
    :href="signupUrl"
    class="max-w-fit flex cursor-pointer items-center rounded bg-#221C02 px-7.5 py-3 text-white"
  >
    <slot>
      {{ $t('订购') }}
    </slot>
  </a>
</template>
<script setup>
  import { defineProps, computed } from 'vue';
  const props = defineProps({
    product: {
      // 产品名称
      type: String,
      validator: (value) => {
        return ['green', 'olive'].includes(value);
      },
    },
    priceId: {
      // 价格id
      type: String,
    },
    url: {
      type: String,
    },
  });
  const signupUrl = computed(() => {
    const result =
      props.url ||
      `${location.origin}${location.pathname}#/signup/index?code=${props.product}${
        props.priceId ? `&priceId=${props.priceId}` : ''
      }`;
    return result;
  });
</script>
