<template>
  <div class="form-container">
    <el-form labelPosition="top" :model="form" ref="formRef" class="form-x">
      <!-- 密码输入及强度提示 -->
      <div class="password-input-with-strength">
        <el-form-item
          :label="passwordLabel"
          :prop="passwordProp"
          :rules="passwordRules"
          :label-width="labelWidth"
        >
          <el-input
            type="password"
            v-model="form.password"
            :placeholder="placeholder"
            :show-password="true"
            autocomplete="new-password"
            @input="checkPasswordStrength"
          ></el-input>
        </el-form-item>

        <div class="strength-container">
          <p class="strength-title">
            {{ $t('密码强度') }}:
            <span :class="passwordStrength.className">
              {{ passwordStrength.text }}
            </span>
          </p>
          <div class="strength-item">
            <span :class="{ 'strength-dot': true, valid: strength.length }"></span>
            <span>{{ $t('stre1') }}</span>
          </div>
          <div class="strength-item">
            <span :class="{ 'strength-dot': true, valid: strength.number }"></span>
            <span>{{ $t('stre2') }}</span>
          </div>
          <div class="strength-item">
            <span :class="{ 'strength-dot': true, valid: strength.upper }"></span>
            <span>{{ $t('stre3') }}</span>
          </div>
          <div class="strength-item">
            <span :class="{ 'strength-dot': true, valid: strength.special }"></span>
            <span>{{ $t('stre4') }}</span>
          </div>
        </div>
      </div>

      <!-- 确认密码 -->
      <el-form-item
        prop="confirmPassword"
        :rules="confirmPasswordRules"
        :label="$t('确认密码')"
        label-width="80px"
      >
        <el-input
          type="password"
          :show-password="true"
          :placeholder="$t('请输入')"
          v-model="form.confirmPassword"
          autocomplete="new-password"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, defineEmits } from 'vue';
  import i18n from '@/i18n';

  // 接收父组件传入的表单对象，注意该组件会修改 pForm 中的 password 与 confirmPassword 字段
  const props = defineProps({
    pForm: {
      type: Object,
      required: true,
    },
  });
  const form = reactive(props.pForm);

  // 确认密码校验规则
  const confirmPasswordRules = computed(() => {
    return [
      {
        required: true,
        message: i18n.t('请输入密码'),
        trigger: ['change', 'blur'],
      },
      {
        validator: (rule, value, callback) => {
          if (value !== form.password) {
            callback(new Error(i18n.t('两次输入密码不一致!')));
          } else {
            callback();
          }
        },
        trigger: ['change', 'blur'],
      },
    ];
  });

  // 密码输入的校验规则与密码强度逻辑（原 passwordInputWithStrength 组件）
  const strength = reactive({
    length: false,
    upper: false,
    number: false,
    special: false,
  });
  const strengthMinimun = ref(false);

  const REG_LENGTH = (password) => /.{8,}/.test(password);
  const REG_NUMBER = (password) => /[0-9]/.test(password) && /[~!#$%^&*_+]/.test(password);
  const REG_UPPER = (password) => /[A-Z]/.test(password) && /[a-z]/.test(password);
  const REG_SPECIAL = (password) => !/[\s]/.test(password) && !/[^\w~!#$%^&*_+]/.test(password);
  const REG_MINIMUM = (password) => /^(?=.*[a-zA-Z])(?=.*\d)[^]{8,16}$/.test(password);

  const passwordStrength = computed(() => {
    let result = { className: '', text: '' };
    const values = Object.values(strength);
    const trueLength = values.filter((v) => v).length;
    if (trueLength === 4) {
      result = { className: 'good', text: i18n.t('强') };
    } else if (trueLength >= 1) {
      result = { className: 'weak', text: i18n.t('弱') };
    }
    return result;
  });

  const passwordRules = computed(() => {
    return [
      {
        required: true,
        message: i18n.t('请输入密码'),
        trigger: ['change', 'blur'],
      },
      {
        validator: (rule, value, callback) => {
          if (!strengthMinimun.value) {
            callback(new Error(i18n.t('密码至少8位，至少1个字母和1个数字')));
          } else if (!strength.special) {
            callback(new Error(i18n.t('stre4')));
          } else {
            callback();
          }
        },
        trigger: ['change', 'blur'],
      },
    ];
  });

  const checkPasswordStrength = () => {
    const passwordValue = form.password;
    strength.length = REG_LENGTH(passwordValue);
    strength.number = REG_NUMBER(passwordValue);
    strength.upper = REG_UPPER(passwordValue);
    strength.special = REG_SPECIAL(passwordValue);
    strengthMinimun.value = REG_MINIMUM(passwordValue);
  };

  // 一些常量设置
  const passwordLabel = i18n.t('创建密码');
  const placeholder = i18n.t('请输入');
  const labelWidth = '80px';
  const passwordProp = 'password';

  // 提交表单
  const emits = defineEmits(['submit']);
  const formRef = ref(null);
  const submitForm = () => {
    return new Promise((resolve, reject) => {
      formRef.value.validate((valid) => {
        if (valid) {
          resolve(valid);
        } else {
          reject(valid);
        }
      });
    });
  };

  defineExpose({
    submitForm,
  });
</script>

<style lang="less" scoped>
  @green: #58c0ad;
  @red: #e36560;

  .strength-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    align-items: center;
    padding: 0 20px;
    margin: 25px 0;
    .strength-title {
      color: #8e8f91;
    }
    .strength-item {
      color: #8e8f91;
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  .weak {
    color: @red;
  }
  .good {
    color: @green;
  }
  .strength-dot {
    flex-shrink: 0;
    width: 5px;
    height: 5px;
    background-color: #999;
    border-radius: 50%;
  }
  .strength-dot.valid {
    background-color: @green;
  }

  .form-x {
    :deep {
      .el-form-item {
        // padding: 10px 20px;
        margin-bottom: 0;
      }
      .el-form-item__label {
        padding: 0;
        color: #222222;
        font-size: 18px;
      }
      .el-input__inner {
        height: 54px;
        line-height: 54px;
      }
      .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
        display: none;
      }
      .el-input__wrapper {
        background-color: #eaebd0;
        border-color: #bababa;
      }
    }
  }
</style>
