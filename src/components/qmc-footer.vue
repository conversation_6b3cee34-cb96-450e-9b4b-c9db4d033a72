<template>
  <!-- v-if="!isOtherCountry" 原来的判断是只有国外版不在展示底部证书,现在改成都不展示-->
  <div class="h-32.5 min-w-5xl flex flex-col items-center justify-center leading-8" v-if="false">
    <div class="flex flex-col items-center justify-center">
      <div>
        <span class="mr-1">{{ $t('ICP备案/许可证号') }}</span>
        <a target="_blank" class="text-#4e40e6" href="https://beian.miit.gov.cn/#/Integrated/index">
          {{ code.ipc }}
        </a>
      </div>

      <a
        target="_blank"
        class="text-#4e40e6"
        href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11011302007305"
      >
        <img :src="record" class="align-middle" />
        {{ code.jga }}
      </a>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue';
  import i18n from '@/i18n';

  import record from '@/assets/images/record.png';
  const currentCountry = import.meta.env.VITE_APP_COUNTRY;
  const isOtherCountry = computed(() => currentCountry !== 'china');
  // console.log('isOtherCountry----', isOtherCountry);
  const code = computed(() => {
    let result = {
      ipc: i18n.t('京ICP备2024053477号-2'),
      jga: i18n.t('京公网安备11011302007313号'),
    };
    if (currentCountry === 'china') {
      result = {
        ipc: i18n.t('京ICP备2024053477号-1'),
        jga: i18n.t('京公网安备11011302007305号'),
      };
    }
    return result;
  });
</script>
