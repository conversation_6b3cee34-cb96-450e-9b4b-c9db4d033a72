html,
body {
  font-family: 'PingFang SC' !important;
  overflow-x: auto;
}

body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
li,
dl,
dt,
dd,
a,
textarea,
input,
button,
span,
em,
strong,
img,
div {
  list-style: none;
  -webkit-touch-callout: none;
  -moz-touch-callout: none;
  -ms-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  -moz-tap-highlight-color: transparent;
  -ms-tap-highlight-color: transparent;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  vertical-align: baseline;
  text-decoration: none;
}

i {
  font-style: normal;
}

/* 改变滚动条默认样式 */
::-webkit-scrollbar-track-piece {
  background-color: #fff;
  /*滚动条凹槽的颜色，还可以设置边框属性 */
}

::-webkit-scrollbar {
  /*滚动条的宽度 */
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  /*滚动条的设置 */
  background-color: #ccc;
  background-clip: padding-box;
  min-height: 28px;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #aaa;
}
