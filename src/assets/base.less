:root {
  --white: #ffffff;
  --color-base: rgba(0, 66, 92, 1);

  --cannon-black: #221c02;
  --crete: #708238;
  --pale-olive: #d4d6be;
  --pigment-indigo: #4b0082;
  --pigment-indigo-light: #5c0099;
  --pigment-indigo-dark: #2a004d;

  --sign-color-base: var(--pigment-indigo);
  --sign-color-gray: #b5b8bb;
  --sign-color-white: #ffffff;
  --sign-color-dark: #000000;
}

.default-btn {
  --el-color-primary-light-3: color-mix(in srgb, var(--pigment-indigo) 100%, white);
  --el-color-primary-light-5: color-mix(in srgb, var(--pigment-indigo) 50%, white);
  --el-color-primary-light-7: color-mix(in srgb, var(--pigment-indigo) 70%, white);
  --el-color-primary-light-9: color-mix(in srgb, var(--pigment-indigo) 90%, white);
  --el-color-primary: var(--white);
  &.el-button--primary {
    --el-color-primary: var(--pigment-indigo);
  }
}
html,
body {
  min-height: 100vh;
  background: #f5f6df;
}
#app {
  min-width: 700px;
  min-height: 100vh;
  word-break: break-word;
}

.bg-icon {
  display: block;
  background-image: url('@/assets/images/sprite.png');
  background-repeat: no-repeat;
}
.bg-icon-1,
.bg-icon-1-dark {
  width: 31px;
  height: 24px;
  background-position: 0 0;
}
.bg-icon-1-dark {
  background-position: 0 -38px;
}

.bg-icon-2,
.bg-icon-2-dark {
  width: 32px;
  height: 28px;
  background-position: -61px 0;
}
.bg-icon-2-dark {
  background-position: -61px -38px;
}

.bg-icon-3,
.bg-icon-3-dark {
  width: 30px;
  height: 25px;
  background-position: -134px 0;
}
.bg-icon-3-dark {
  background-position: -134px -38px;
}

.bg-icon-4,
.bg-icon-4-dark {
  width: 30px;
  height: 25px;
  background-position: -206px 0;
}
.bg-icon-4-dark {
  background-position: -206px -38px;
}

.bg-icon-arrow {
  width: 38px;
  height: 15px;
  background-position: 0 -78px;
}

.bg-icon-check {
  width: 13px;
  height: 9px;
  background-position: -94px -78px;
}

.bg-icon-eye {
  width: 20px;
  height: 16px;
  background-position: -94px -78px;
}

.bg-icon-close-eye {
  width: 20px;
  height: 15px;
  background-position: -162px -78px;
}
