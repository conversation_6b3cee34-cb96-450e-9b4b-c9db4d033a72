import './assets/main.less';
import 'virtual:uno.css';
import price from '@/config/price.js';
import register from '@/register';

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import VueGtag from 'vue-gtag-next';

import App from './App.vue';
import router from './router';
import i18nIn from './i18n';

const app = createApp(App);

app.use(i18nIn.instance);
app.use(createPinia());
app.use(router);
app.use(register);

// Configure Google Analytics with Vue Router integration
app.use(
  VueGtag,
  {
    property: {
      id: 'G-HL9HH3KX11',
    },
    appName: 'Olivegreen CRM',
    enabled: true,
    bootstrap: true,
  },
  router,
);

// 将currentPrices挂载到全局属性上
app.config.globalProperties.$price = price;

app.mount('#app');
