import request from '@/utils/request/index.js';

// 留言
export const submitInquiryAPI = (body) => {
  return request.post(`callback/v2/inquiry/sales/green`, body);
};

// 填写基本信息注册
export const signupApplyAPI = (body) => {
  return request.post(`mto/api/v2/signup/apply`, body);
};

// 设置密码页面预先调用
export const signupAcceptAPI = (appCode, body) => {
  return request.post(`${appCode}/api/v2/signup/accept`, body);
};

// 注册
export const signupActivateAPI = (appCode, body) => {
  return request.post(`${appCode}/api/v2/signup/activate`, body);
};

// 获取session ()
export const checkoutSessionAPI = (body) => {
  return request.post(`crm/api/v2/pay/stripe/checkout/session`, body);
};

// 创建订阅
export const subscriptionSessionAPI = (body) => {
  return request.post(`crm/api/v2/pay/stripe/subscription/session`, body);
};

/**
 * 获取登录人信息
 */

export const getUserInfoApi = (body) => {
  return request.get(`user/api/v2/user/me`, body);
};

/**
 * @description: 机构版 获取注销账号验证码
 * @param {*} username 用户名
 * @param {*} usernameType "账号类型: 0: 手机号，2: 邮箱"
 */
export const accountDeletionCodeApi = ({ username, usernameType }) => {
  console.log('请求参数:', username, usernameType);

  return request.post(`olive/user/api/a/delete/attempt`, {
    data: {
      username,
      usernameType,
    },
  });
};

/**
 * @description: 机构版 注销账号
 * @param {*} username 用户名
 * @param {*} verificationCode 验证码
 */
export const accountDeletionApi = ({ username, password }) => {
  return request.post(`olive/user/api/a/delete`, {
    data: {
      username,
      password,
    },
  });
};

export default {
  signupApplyAPI,
  signupAcceptAPI,
  signupActivateAPI,
  checkoutSessionAPI,
  subscriptionSessionAPI,
  submitInquiryAPI,
  accountDeletionCodeApi,
  accountDeletionApi,
};
