import { createRouter, createWebHashHistory } from 'vue-router';
import layoutVue from '@/views/qm-layout.vue';
import homeVue from '@/views/home/<USER>';
// import contactVue from '@/views/qm-contact.vue'
import notFoundVue from '@/views/qm-notFound.vue';
import priceVue from '@/views/qm-price.vue';
import signupVue from '@/views/signup/index.vue';
import checkoutVue from '@/views/checkout/index.vue';
import otherSignupPasswordVue from '@/views/otherSignup/password.vue';
import privacyPolicy from '@/views/pervacyAndterms/privacy-policy.vue';
import termsService from '@/views/pervacyAndterms/terms-service.vue';
import accountDeletion from '@/views/accountDeletion/accountDeletion.vue';

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'layout',
      component: layoutVue,
      redirect: 'home',
      children: [
        {
          path: '/home',
          name: 'home',
          component: homeVue,
        },
        // {
        //   path: '/contact',
        //   name: 'contact',
        //   component: contactVue
        // },
        {
          path: '/price',
          name: 'price',
          component: priceVue,
        },
      ],
    },
    {
      path: '/signup/:stepName',
      name: 'signup',
      component: signupVue,
    },
    {
      path: '/checkout/:typeName', // 订阅支付成功页面
      name: 'checkout',
      component: checkoutVue,
    },
    {
      path: '/signup/mto/password', // mto 内部员工
      name: 'signupMtoPassword',
      component: otherSignupPasswordVue,
    },
    {
      path: '/signup/employee/password', // 客户内部员工
      name: 'signupEmployeePassword',
      component: otherSignupPasswordVue,
    },
    {
      path: '/signup/b2c/password', // 客户的客户
      name: 'signupB2cPassword',
      component: otherSignupPasswordVue,
    },
    {
      path: '/terms-service', // 服务条款
      name: 'termsService',
      component: termsService,
    },
    {
      path: '/privacy-policy', // 隐私政策
      name: 'privacyPolicy',
      component: privacyPolicy,
    },
    {
      path: '/account-deletion', // 隐私政策
      name: 'accountDeletion',
      component: accountDeletion,
    },
    {
      path: '/:pathMatch(.*)*/', // '["abc","sdfb","asdflj"]'
      name: 'notFound',
      component: notFoundVue,
    },
  ],
});

export default router;
