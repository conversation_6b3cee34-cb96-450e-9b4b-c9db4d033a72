import LocalCache from '@/utils/localCache';
import numberFormats from '@/i18n/numberFormats';
import localeEn from '@/i18n/lang/en.json';
import localeZh from '@/i18n/lang/zh.json';
import localeJa from '@/i18n/lang/ja.json';

// element
import zhCnEl from 'element-plus/dist/locale/zh-cn.mjs';
import jaEl from 'element-plus/dist/locale/ja.mjs';
import enEl from 'element-plus/dist/locale/en.mjs';
import { createI18n } from 'vue-i18n';

export const localeNameObj = {
  en: {
    name: 'en',
  },
  zh: {
    name: 'zh',
  },
  ja: {
    name: 'ja',
  },
};
class i18nIn {
  static instance;
  constructor() {
    if (!i18nIn.instance) {
      const userLocale = navigator.language;
      const userLang = LocalCache.getCache('locale')?.name || userLocale || 'en';
      const locale = /^zh/.test(userLang) ? 'zh' : /^ja/.test(userLang) ? 'ja' : 'en';
      LocalCache.setCache('locale', localeNameObj[locale]);
      i18nIn.instance = createI18n({
        legacy: false, // 设为false 使用 Composition API
        fallbackLocale: 'en', // 切换语言出错,回退的语言
        locale: locale || 'en',
        messages: {
          en: localeEn,
          zh: localeZh,
          ja: localeJa,
        },
        // 数字格式化,但是格式化只能是整数和浮点数,不能动态转换. 所以暂时没有使用
        numberFormats: numberFormats,
      });
      // 获取当前的语言
      // i18nIn.setLocale(locale)
    }
  }
  // 切换语言可以放到pinia里,使用useI18n().locale切换
  static setLocale = (localeName) => {
    // 在你的组件或相应的场景中
    console.log('setLocale---', localeName);
    // 没有定义语言,默认使用中文
    const currentLocale = localeNameObj[localeName] || localeNameObj.zh;
    console.log('-------', currentLocale);
    // 把当前语言设置到locale里
    LocalCache.setCache('locale', currentLocale);
    i18nIn.instance.global.locale = localeName;
  };
  static t = (name) => {
    return i18nIn.instance.global.t(name);
  };
  static getLocale = () => {
    return i18nIn.instance.global.locale.value;
  };
}

new i18nIn();
export default i18nIn;

// 获取elementplus 对应的语言包,在App.vue 中切换 element-plus 语言用
export const getElementPlusLocale = () => {
  const localeObj = {
    zh: zhCnEl,
    ja: jaEl,
    en: enEl,
  };
  // 设置element plus语言,默认中文
  const localeName = LocalCache.getCache('locale')?.name || 'zh';

  return localeObj[localeName];
};
