<template>
  <div class="yj-form">
    <slot name="header"></slot>
    <el-form
      :model="value"
      :label-width="labelWidth"
      ref="ruleForm"
      :disabled="disabled"
      :label-position="labelPosition"
      @submit.prevent
    >
      <!--  element-ui的input的value属性不再支持, element-plus 需要使用 model-value -->
      <el-row :gutter="gutter">
        <template v-for="item in formItems" :key="item.label">
          <el-col v-bind="{ ...colLayout, ...item.colLayout }">
            <el-form-item
              v-if="item.hidden != true"
              :label="item.label"
              :style="item.style || itemStyle"
              :prop="item.prop"
              :label-width="item.labelWidth"
              :rules="item.rules"
              :ref="item.refName"
              :class="{ 'is-required': item.required }"
            >
              <slot v-if="item.slotName" :name="item.slotName" v-bind="item"></slot>
              <el-input
                v-if="item.type === 'input' || item.type === 'password'"
                :disabled="item.disabled || item?.otherOptions?.disabled"
                :placeholder="item.placeholder"
                :show-password="item.type === 'password'"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
                v-bind="item?.otherOptions"
              ></el-input>

              <el-select
                v-else-if="item.type === 'select'"
                :disabled="item?.disabled"
                :placeholder="item.placeholder"
                style="width: 100%"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
              >
                <el-option
                  v-for="option in item.options"
                  :key="option.value"
                  :label="option.label"
                  :model-value="option.value"
                ></el-option>
              </el-select>
              <el-select
                v-else-if="item.type === 'selectTree'"
                :placeholder="item.placeholder"
                style="width: 100%"
                :disabled="!!value.selectTreeDisabled"
                :clearable="item.clearable"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
                @clear="selectTreeClearHandle(item)"
                ref="selectblur"
              >
                <el-option style="height: 100%; background: #fff" value="code">
                  <el-tree
                    :data="item.otherOptions.data"
                    accordion
                    node-key="code"
                    ref="updatetree"
                    highlight-current
                    :props="item.otherOptions.defaultProps"
                    :expand-on-click-node="false"
                    @node-click="
                      (data) => {
                        nodeChange(data, value, item);
                      }
                    "
                  ></el-tree>
                </el-option>
              </el-select>
              <el-date-picker
                v-else-if="item.type === 'datepicker'"
                :disabled="item.disabled"
                v-bind="item.otherOptions"
                :placeholder="item.placeholder"
                style="width: 100%"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
                :value-format="item.valueFormat || 'timestamp'"
              ></el-date-picker>
              <el-cascader
                v-else-if="item.type === 'cascader'"
                :disabled="item.disabled"
                v-bind="item.otherOptions"
                :placeholder="item.placeholder"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
                style="width: 100%"
              ></el-cascader>
              <el-autocomplete
                ref="el-autocomplete"
                v-else-if="item.type === 'autocomplete'"
                :disabled="item?.disabled"
                style="width: 100%"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
                :placeholder="item.placeholder"
                v-bind="item.otherOptions"
                v-on="item.listeners"
              ></el-autocomplete>
              <el-select
                v-else-if="item.type === 'filterable'"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
                v-bind="item.otherOptions"
                :disabled="item.disabled"
                filterable
                remote
                reserve-keyword
                :placeholder="item.placeholder"
                :remote-method="remoteMethod"
                :loading="loading"
                @change="
                  (event) => {
                    selectChange(event, item.field);
                  }
                "
              >
                <el-option
                  v-for="(item, index) in options"
                  :key="item.value + '-' + index"
                  :label="item.value"
                  :model-value="item"
                ></el-option>
              </el-select>
              <el-radio-group
                v-if="item.type == 'radio'"
                :model-value="value[item.field]"
                @input="changeValue($event, item.field)"
              >
                <el-radio v-for="radio in item.radioList" :key="radio.label" :label="radio.label">
                  {{ radio.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <slot name="footer"></slot>
  </div>
</template>

<script setup>
  import { ref, watch, defineProps, defineEmits } from 'vue';

  const props = defineProps({
    labelPosition: {
      type: String,
      default: 'right',
    },
    gutter: {
      type: Number,
      default: 20,
    },
    doSearchTeacher: {
      type: Function, // 下拉框中远程搜索 “搜索老师” 用的方法
    },
    value: {
      type: Object, // 表单数据对象
      required: true,
    },
    formItems: {
      // 每一项 formItem
      type: Array,
      // 非基本数据类型的默认值，必须用箭头函数（数组、对象）
      default: () => [],
    },
    labelWidth: {
      //表单 label 的宽度
      type: String,
      default: '100px',
    },
    // 修改el-form-item
    itemStyle: {
      type: Object,
      default: () => ({ padding: '10px 20px' }),
    },
    // 修改col的span尺寸，不传为默认值
    colLayout: {
      // 可以传入el-col 的所有属性
      type: Object,
      default: () => ({
        // xl: 6, // >1920
        // lg: 8,
        // md: 12,
        sm: 24,
        xs: 24,
      }),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['input', 'selectValue', 'selectTreeNodeChange']);

  // const formData = ref({ ...props.value })
  // watch(
  //   formData,
  //   (newVal) => {
  //     emit('input', newVal)
  //   },
  //   { deep: true }
  // )

  const ruleForm = ref(null);
  const changeValue = (value, field) => {
    emit('input', { ...props.value, [field]: value });
  };
  const loading = ref(true);
  const options = ref([]);

  const remoteMethod = async (query) => {
    if (query !== '') {
      loading.value = true;
      const res = await props.doSearchTeacher(query);
      loading.value = false;
      options.value = res;
    } else {
      options.value = [];
    }
  };

  const selectChange = (event, field) => {
    emit('selectValue', {
      value: event,
      field,
    });
  };

  const nodeChange = (data, value, item) => {
    value[item.field] = data.name;
    if (item.$refs.selectblur) {
      item.$refs.selectblur[0].blur();
    }
    emit('selectTreeNodeChange', { [item.field]: data.code });
  };

  const selectTreeClearHandle = (item) => {
    emit('selectTreeNodeChange', { [item.field]: '' });
  };
  // 必须要导出，才能在父组件中使用,vue3 特性
  defineExpose({
    ruleForm,
  });
</script>

<style scoped>
  .yj-form {
    padding: 20px 20px 0 0;
  }
</style>
