const images = import.meta.glob(`@/assets/images/**/*.png`, { eager: true });
/**
 * 从指定目录加载图片
 *
 * @param directoryPath 目录路径
 * @returns 返回一个包含图片URL的对象
 */

export const loadImagesFromDirectory = (directoryPath) => {
  // 加载目录下文件
  const list = Object.entries(images).filter((v) => v[0].includes(directoryPath));
  return list.reduce((acc, [key, value]) => {
    const url = value.default;
    const newKey = key.replace(directoryPath, '');
    acc[newKey] = url;
    return acc;
  }, {});
};

/**
 * 根据子目录获取图片列表
 *
 * @param imageUrls 图片URL对象
 * @param subdirectory 子目录名称
 * @returns 返回匹配子目录的图片URL数组
 */
export const getImagesBySubdirectory = (imageUrls, subdirectory) => {
  const result = [];
  // 遍历图片URL对象
  for (const key in imageUrls) {
    // 如果图片路径以指定的子目录开头
    if (key.startsWith(`/${subdirectory}/`)) {
      // 将图片URL添加到结果数组中
      result.push(imageUrls[key]);
    }
  }

  // 返回匹配子目录的图片URL数组
  return result;
};
