import APICustomer from '@/service/APIcustomer.js';
import { objectToQueryString } from '@/utils/utils.js';

class MyStripe {
  constructor() {
    // 绑定this
    this.getUrl = this.getUrl.bind(this);
    this.getCheckoutSession = this.getCheckoutSession.bind(this);
  }

  getUrl() {
    const domain = `https://${import.meta.env.VITE_FRONTEND_URL}/#`;
    const result = {
      successUrl: `${domain}/checkout/success`,
      cancelUrl: `${domain}/checkout/failure`,
    };
    return result;
  }
  async getCheckoutSession(opt) {
    const { successUrl, cancelUrl } = this.getUrl();
    const appCode = opt.code;
    // 为跳转链接添加额外参数
    const successUrlQuery = objectToQueryString(opt.successUrlQuery);
    const cancelUrlQuery = objectToQueryString(opt.cancelUrlQuery);
    const resultSuccessUrl = successUrl + (opt.successUrlQuery ? `/?${successUrlQuery}` : '');
    const resultCancelUrl = cancelUrl + (opt.cancelUrlQuery ? `/?${cancelUrlQuery}` : '');
    const result = await APICustomer.checkoutSessionAPI({
      data: {
        priceId: opt.priceId,
        trialPeriodDays: opt.trialPeriodDays,
        successUrl: resultSuccessUrl,
        // cancelUrl: resultCancelUrl,
      },
      config: {
        baseURL: import.meta.env.VITE_API_BASE_URL + appCode,
      },
      mergeHeaders: true,
      externalHeaders: {
        'app-code': appCode,
      },
    });
    return result;
  }
  async subscriptionSession(opt) {
    const appCode = opt.code;
    const result = await APICustomer.subscriptionSessionAPI({
      data: {
        priceId: opt.priceId, // Stripe的定价ID,
        trialPeriodDays: opt.trialPeriodDays, // 试用的日期数
      },

      config: {
        baseURL: import.meta.env.VITE_API_BASE_URL + appCode,
      },
      mergeHeaders: true,
      externalHeaders: {
        'app-code': appCode,
      },
    });

    return result;
  }
}
const instance = new MyStripe();
Object.freeze(instance);

export default instance;
