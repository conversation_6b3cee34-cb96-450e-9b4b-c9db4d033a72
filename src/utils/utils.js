// 对象转换为查询字符串
export const objectToQueryString = (obj) => {
  const keyValuePairs = [];

  for (const key in obj) {
    keyValuePairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`);
  }

  return keyValuePairs.join('&');
};
// 查询字符串转对象
export const queryToDecodeObject = (query) => {
  const result = {};
  for (const key in query) {
    result[decodeURIComponent(key)] = decodeURIComponent(query[key]);
  }
  return result;
};
