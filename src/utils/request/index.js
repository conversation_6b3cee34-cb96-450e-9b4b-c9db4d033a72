import axios from 'axios';
import i18n from '@/i18n';
import LocalCache from '@/utils/localCache';

import { ElLoading } from 'element-plus';

let loadingInstance = {};

const localeRaw = i18n.getLocale();
// 由于后台只接受zh,使用zh-CN会导致报错,所以把zh-CN改为zh
const locale = localeRaw === 'zh-CN' ? 'zh' : localeRaw;
// console.log('------localeRaw', localeRaw);
// console.log('------locale', locale);

axios.interceptors.response.use(
  function (response) {
    return response.data;
  },
  function (error) {
    console.error('全局请求错误:', error);
    return Promise.reject(error);
  },
);

class HttpRequest {
  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL;
    this.timeout = 10000;
  }

  setInterceptor(instance, externalHeaders = {}, mergeHeaders = false) {
    instance.interceptors.request.use((config) => {
      console.log('请求配置:', config);
      console.log('locale-----', locale);

      config.headers['Accept-Language'] = locale;

      const setHeader = () => {
        // TODO 默认的header设置设置可以写这里,现在暂时不需要
        const token = LocalCache.getCache('token');
        if (token) {
          config.headers['Authorization'] = token;
        }

        // 机构版/学校版默认角色
        config.headers['default-role-type'] = 2;
        const customerId = LocalCache.getCache('customerId');
        if (customerId) {
          config.headers['customer-id'] = customerId;
        }
      };

      if (mergeHeaders) {
        //  合并外部的header
        setHeader();
        config.headers = { ...config.headers, ...externalHeaders };
      } else if (Object.keys(externalHeaders).length) {
        // 使用外部传入的headers，覆盖默认的headers
        config.headers = { ...config.headers, ...externalHeaders };
      } else {
        // 在拦截器中设置headers
        setHeader();
      }

      console.log('最终请求头:', config.headers);
      console.log('最终请求数据:', config.data);

      return config;
    });

    instance.interceptors.response.use(
      (res) => {
        loadingInstance.close();
        console.log('响应数据:', res.data);
        return res.data;
      },
      (err) => {
        loadingInstance.close();
        console.error('请求错误:', err);
        if (err.response) {
          console.error('错误状态码:', err.response.status);
          console.error('错误响应:', err.response.data);
        }
        return Promise.reject(err);
      },
    );
  }

  request({ options, externalHeaders = {}, mergeHeaders = false }) {
    const instance = axios.create();
    loadingInstance = ElLoading.service();
    const config = {
      baseURL: this.baseURL,
      timeout: this.timeout,
      ...options,
    };
    this.setInterceptor(instance, externalHeaders, mergeHeaders);
    return instance(config);
  }

  // 需要外部传入headers, 需要传入externalHeaders和mergeHeaders
  get(url, { data = {}, config = {}, externalHeaders = {}, mergeHeaders = false } = {}) {
    return this.request({
      options: {
        url,
        method: 'get',
        ...data,
        ...config,
      },
      externalHeaders,
      mergeHeaders,
    });
  }

  post(url, { data = {}, config = {}, externalHeaders = {}, mergeHeaders = false } = {}) {
    return this.request({
      options: {
        url,
        method: 'post',
        data,
        ...config,
      },
      externalHeaders,
      mergeHeaders,
    });
  }

  put(url, { data = {}, config = {}, externalHeaders = {}, mergeHeaders = false } = {}) {
    return this.request({
      options: {
        url,
        method: 'put',
        data,
        ...config,
      },
      externalHeaders,
      mergeHeaders,
    });
  }

  postFormdata(url, { data = {}, config = {}, externalHeaders = {}, mergeHeaders = false }) {
    const formData = new FormData();
    for (const key in data) {
      for (const item of data[key]) {
        formData.append(key, item);
      }
    }
    return this.request(
      {
        url,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        ...config,
      },
      externalHeaders,
      mergeHeaders,
    );
  }
}

export default new HttpRequest();
