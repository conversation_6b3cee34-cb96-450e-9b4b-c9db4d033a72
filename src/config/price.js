// 测试价格Id
const testPriceIdObj = {
  china: {
    monthlyPriceId: 'price_1PnGdjJQ6NsEpmsCg0hKr5Wf',
    yearlyPriceId: 'price_1PnGeBJQ6NsEpmsC0ZDeEFrj',
  },
  otherCountries: {
    monthlyPriceId: 'price_1PnGdjJQ6NsEpmsCg0hKr5Wf',
    yearlyPriceId: 'price_1PnGeBJQ6NsEpmsC0ZDeEFrj',
  },
};
// 生产价格Id
// TODO 原来的需求是国外国内价格不同，打出不同的包。但是最新的需求是都用一个价格,这里都改成一个价格。
const productionPriceIdObj = {
  china: {
    monthlyPriceId: 'price_1PnGQWJQ6NsEpmsC3eDiyCh6',
    yearlyPriceId: 'price_1QSCneJQ6NsEpmsC8CbbZuIF',
  },
  otherCountries: {
    monthlyPriceId: 'price_1PnGQWJQ6NsEpmsC3eDiyCh6',
    yearlyPriceId: 'price_1QSCneJQ6NsEpmsC8CbbZuIF',
  },
};
// 不同环境的价格Id
const envPriceIdObj = {
  development: testPriceIdObj,
  staging: testPriceIdObj,
  production: productionPriceIdObj,
};

const env = import.meta.env.VITE_NAME;
const contry = import.meta.env.VITE_APP_COUNTRY;
// 当前环境的china价格Id
const priceIdChinaObj = envPriceIdObj[env].china;
// 当前环境的other价格Id
const priceIdOtherCountriesObj = envPriceIdObj[env].otherCountries;
// 价格对象
const price = {
  china: {
    monthlyPrice: '￥101', // 展示的月付金额
    monthlyPriceId: priceIdChinaObj.monthlyPriceId,
    yearlyPrice: '￥1001', // 展示的年付金额
    yearlyPriceId: priceIdChinaObj.yearlyPriceId,
  },
  otherCountries: {
    monthlyPrice: '$88', // 展示的月付金额
    monthlyPriceId: priceIdOtherCountriesObj.monthlyPriceId,
    yearlyPrice: '$880', // 展示的年付金额
    yearlyPriceId: priceIdOtherCountriesObj.yearlyPriceId,
  },
};
export default price[contry];
