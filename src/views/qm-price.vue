<template>
  <div class="relative min-w-1200px">
    <div
      class="l-0 [-webkit-clip-path:ellipse(85%_100%_at_50%_100%)] [clip-path:ellipse(85%_100%_at_50%_100%)] absolute bottom-0 h-436px w-full bg-#EAEBD0"
    ></div>
    <div class="price-x relative z-2 pb-40px">
      <p class="h-200px text-center text-36px font-bold leading-200px">
        {{ $t('选择您的订阅方案') }}
      </p>
      <div class="mx-auto w-5xl flex justify-center gap-80px">
        <div
          class="prod-item h-580px min-h-400px w-424px flex flex-col overflow-hidden rounded-xl bg-#fff text-18px"
        >
          <div class="border-t-14px border-#D1CD49 rounded border-solid py-20px text-center">
            <div class="mb-10px px-20px text-26px font-bold">
              {{ $t('EduRP橄榄版') }}
            </div>
            <div class="text-14px text-#898989">
              {{ $t('为培训中心量身设计') }}
            </div>
          </div>
          <div
            class="item-x mx-20px flex flex-1 flex-col border-y-2px border-#f4f4f4 border-solid py-15px pl-40px pt-20px leading-40px"
          >
            <div class="text-left -ml-24px">
              <input
                type="radio"
                id="month"
                name="plan"
                value="month"
                v-model="planType"
                class="cursor-pointer"
              />
              <label for="month" class="ml-10px cursor-pointer">
                <span class="font-300">{{ $t('按月支付') }}</span>
                <i class="px-10px">-</i>
                <span>
                  {{ $t('¥365 / 月') }}
                </span>
              </label>
            </div>
            <div class="mb-13px mr-0 mt-13px text-left leading-20px -ml-24px">
              <input
                type="radio"
                id="year"
                name="plan"
                value="year"
                v-model="planType"
                class="cursor-pointer"
              />
              <label for="year" class="ml-10px cursor-pointer leading-30px">
                <span>
                  <span class="font-300">{{ $t('按年支付') }}</span>
                  <i class="px-10px">-</i>
                  <span>{{ $t('¥280 / 月') }}</span>
                </span>
                <span class="ml-24px inline-block w-full text-18px text-#676767 font-300">
                  ({{ $t('每年收费 ¥3,330') }})
                </span>
              </label>
            </div>
          </div>
          <div class="box-border h-170px pt-70px">
            <signupBtn
              product="olive"
              :priceId="olivePriceId"
              class="flex justify-center leading-54px !mx-30px !h-54px !max-w-full !rounded-md !bg-#E7E487 !py-0 !text-center !text-#262004"
            >
              {{ $t('订购2') }}
            </signupBtn>

            <div class="mt-10px text-center text-16px">{{ $t('包含30天免费试用') }}</div>
          </div>
        </div>
        <div
          class="prod-item h-580px min-h-400px w-424px flex flex-col overflow-hidden rounded-xl bg-#fff text-20px"
        >
          <div class="border-t-14px border-#939F3D rounded border-solid py-20px text-center">
            <div class="mb-10px px-20px text-26px font-bold">
              {{ $t('EduRP智校版') }}
            </div>
            <div class="text-14px text-#898989">
              {{ $t('专为K-12学校打造') }}
            </div>
          </div>

          <div
            class="item-x mx-20px flex flex-1 flex-col border-y-2px border-#f4f4f4 border-solid py-15px pl-20px pt-20px text-18px leading-40px"
          >
            <div class="mb-6px font-500">
              <el-icon color="#76C845" size="22" class="align-middle -mt-6px">
                <Check />
              </el-icon>
              {{ $t('支持定制化') }}
            </div>
            <div>{{ $t('联系我们的销售团队了解定价详情') }}</div>
            <div class="mb-6px mt-6px leading-30px">
              <a href="javascript:void(0)" @click="handleEmailClick" class="text-#010F8B">
                📮{{ $t('点击这里发送邮件') }}
              </a>
              <div class="text-16px text-#A5A5A5"><EMAIL></div>
            </div>
          </div>

          <div class="box-border h-170px pt-70px">
            <signupBtn
              product="green"
              :priceId="greenPriceId"
              class="flex justify-center leading-54px !mx-30px !h-54px !max-w-full !rounded-md !bg-#939F3D !py-0 !text-center"
            >
              {{ $t('订购2') }}
            </signupBtn>

            <div class="mt-10px text-center text-16px">
              {{ $t('提供30天免费试用') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <concatForm v-model="emailModalVisible"></concatForm>
</template>
<script setup>
  import signupBtn from '@/components/home/<USER>';
  import concatForm from '@/components/concat-form.vue';
  import { ref, reactive, computed, getCurrentInstance } from 'vue';

  import i18n from '@/i18n';

  const instance = getCurrentInstance();
  const price = instance.appContext.config.globalProperties.$price;

  const planType = ref('month');
  const olivePriceId = computed(() =>
    planType.value === 'month' ? price.monthlyPriceId : price.yearlyPriceId,
  );
  // TODO  green注册调后台接口需要传一个priceId，但是不会真的在stripe中使用。green还没有定价ID,所以先随便写一个。
  const greenPriceId = computed(() =>
    planType.value === 'month' ? price.monthlyPriceId : price.yearlyPriceId,
  );

  const emailForm = reactive({
    email: '',
  });
  const emailModalVisible = ref(false);
  const handleEmailClick = () => {
    emailModalVisible.value = true;
    // <EMAIL>
  };
  const handleEmailModalClose = () => {
    emailModalVisible.value = false;
  };

  // console.log('price----', price);
  const switchType = ref(null);
  // 年付,月付文字
  const switchText = computed(() => {
    return !switchType.value ? i18n.t('月付') : i18n.t('年付');
  });

  const locale = computed(() => {
    return i18n.getLocale();
  });

  /**
   * 根据货币类型格式化价格
   *
   * @param price 价格
   * @param locale 货币类型
   * @returns 返回格式化后的价格字符串
   */
  const pricingList = computed(() => {
    const currentCountry = import.meta.env.VITE_APP_COUNTRY;
    let monthlyPrice, yearlyPrice;
    let monthlyPriceId = price.monthlyPriceId;
    let yearlyPriceId = price.yearlyPriceId;
    monthlyPrice = price.monthlyPrice;
    yearlyPrice = price.yearlyPrice;

    const result = [
      {
        title: i18n.t('Olive'),
        product: 'olive',
        monthlyPrice: {
          price: monthlyPrice,
          unit: i18n.t('月'),
          // payment: '1 - 80 students'
          priceId: monthlyPriceId,
        },
        yearlyPrice: {
          price: yearlyPrice,
          unit: i18n.t('年'),
          priceId: yearlyPriceId,
        },
        buttonText: i18n.t('免费试用'),
        content: [
          // '$150/Month/81 - 200 students',
          // '$200/Month/201 - 400 students',
          // '$300/Month/401 - 600 students',
          // '$388/Month/401+ students'
        ],
        bgColor: '!bg-#4E40E6',
        borderColor: 'border-#4E40E6',
      },
      {
        title: i18n.t('Green'),
        product: 'green',
        // titlet2: i18n.t('联系我们'),
        buttonText: i18n.t('免费试用'),
        monthlyPrice: {
          price: monthlyPrice,
          unit: i18n.t('月'),
          // payment: '1 - 80 students'
          priceId: monthlyPriceId,
        },
        yearlyPrice: {
          price: yearlyPrice,
          unit: i18n.t('年'),
          priceId: yearlyPriceId,
        },
        content: [],
        bgColor: '!bg-#17B4CE',
        borderColor: 'border-#17B4CE',
      },
    ];

    if (switchType.value === 1) {
      // 年付
      result[0].price = result[0].yearlyPrice;
      result[1].price = result[1].yearlyPrice;
    } else {
      // 月付
      result[0].price = result[0].monthlyPrice;
      result[1].price = result[1].monthlyPrice;
    }
    return result;
  });
  const handleSwitch = (type) => {
    switchType.value = type;
  };
</script>
<style lang="less" scoped>
  input[type='radio'] {
    /* 移除默认样式 */
    appearance: none;
    -webkit-appearance: none;

    /* 自定义样式 */
    width: 16px;
    height: 16px;
    border: 2px solid #999;
    border-radius: 50%;
    background-color: transparent; /* 背景透明 */

    /* 选中时的样式 */
    &:checked {
      border-color: #d1cd49;
      background: transparent;
      position: relative;

      &::after {
        content: '';
        width: 8px;
        height: 8px;
        background: #d1cd49;
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  // .price-x {
  //   position: relative;
  //   width: 100%;
  //   padding-top: 100px;
  //   background: url('@/assets/images/bg-rounded.png') 0 0 no-repeat;
  //   background-size: 100% 671px;
  // }
</style>
