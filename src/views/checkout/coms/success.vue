<template>
  <div class="payment-x payment-item">
    <div class="payment-content">
      <p class="icon-x">
        <el-icon class="payment-success__icon"><CircleCheck /></el-icon>
      </p>
      <div class="payment-success__title">{{ $t('支付成功') }}</div>
      <div class="payment-success__message">{{ $t('感谢您的购买，订单已成功处理。') }}</div>
      <el-button class="go-home-btn payment-success__button" type="primary" @click="goToHome">
        {{ $t('返回主页') }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
  import LocalCache from '@/utils/localCache';
  import { computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useRouteData } from '@/hooks/useRouteData';

  import myStripe from '@/utils/stripe';

  const GREEN_URL = import.meta.env.VITE_GREEN_URL;
  const OLIVE_URL = import.meta.env.VITE_OLIVE_URL;
  const urlObj = {
    green: GREEN_URL,
    olive: OLIVE_URL,
  };

  const route = useRoute();
  const router = useRouter();

  const { query } = useRouteData();
  const isSignup = computed(() => !!query.value.signup);

  async function initSubscription() {
    try {
      const result = await myStripe.subscriptionSession({
        code: query.value.code,
        priceId: query.value.priceId, // Stripe的定价ID,
        trialPeriodDays: Number(query.value.d) ? Number(query.value.d) : 0, // 试用的日期数
      });

      console.log('result------', result);
    } catch (error) {
      console.error('error----', error);
    }
  }

  function goToHome() {
    if (isSignup.value) {
      goToLogin();
      return;
    }
  }

  const currentLocaleName = LocalCache.getCache('locale')?.name || 'zh'; // zh ja en
  function goToLogin() {
    const baseUrl = urlObj[query.value.code];
    const url = `${baseUrl}/login?sgtoken=${query.value.sgtoken}&customerId=${query.value?.customerId}&lang=${currentLocaleName}`;
    console.log('url-----', url);
    location.href = url;
  }

  onMounted(() => {
    // console.log('query-----', query.value);
    // console.log('$route---', route);

    if (isSignup.value) {
      initSubscription();
    }
  });
</script>

<style lang="less" scoped>
  .payment-x {
    height: 390px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .payment-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .payment-success__button {
    margin-top: 20px;
  }

  .go-home-btn {
    background-color: #221c02;
    border-color: #221c02;
  }

  .payment-success__icon {
    font-size: 64px;
    color: #72c040;
  }
  .payment-success__title {
    font-size: 24px;
    margin: 20px 0;
    color: #333;
  }
  .payment-success__message {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
  }
</style>
