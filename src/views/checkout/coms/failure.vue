<template>
  <div class="payment-x payment-item">
    <div class="payment-content">
      <p class="icon-x">
        <el-icon class="payment-failure__icon"><CircleClose /></el-icon>
      </p>

      <div class="payment-failure__title">{{ $t('返回主页') }}</div>
      <div class="payment-failure__message">{{ $t('很抱歉，您的支付未能成功，请稍后重试。') }}</div>

      <el-button type="primary" @click="retryPayment" class="go-home-btn payment-failure__button">
        {{ $t('返回主页') }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  const retryPayment = () => {
    // 跳回到首次注册的页面
    location.href = `${location.origin}/#/payment/index`;
  };
</script>

<style lang="less" scoped>
  .payment-x {
    height: 390px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .payment-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .payment-failure {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
  }
  .payment-failure__icon {
    font-size: 64px;
    color: #f56c6c;
  }
  .payment-failure__title {
    font-size: 24px;
    margin: 20px 0;
    color: #333;
  }
  .payment-failure__message {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
  }
  .payment-failure__button {
    margin-top: 20px;
  }

  .go-home-btn {
    background-color: var(--pigment-indigo);
    border-color: var(--pigment-indigo);
  }
</style>
