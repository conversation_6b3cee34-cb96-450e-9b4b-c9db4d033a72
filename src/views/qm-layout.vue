<template>
  <div class="min-h-100vh break-words">
    <qmcHeader></qmcHeader>
    <div class="h-[calc(100vh_-_90px)] pt-90px">
      <router-view></router-view>
    </div>
    <qmcFooter></qmcFooter>
  </div>
</template>
<script setup>
  import qmcHeader from '@/components/qmc-header.vue';
  import qmcFooter from '@/components/qmc-footer.vue';
  import { RouterView } from 'vue-router';
  import { provide, ref } from 'vue';
  const clickNavName = ref(null);
  const updateClickNavName = (value) => {
    clickNavName.value = value || '';
  };

  provide('navName', {
    clickNavName,
    updateClickNavName,
  });
</script>
<style lang="less" scoped></style>
