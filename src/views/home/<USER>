<template>
  <div class="mx-auto min-w-1230px pb-25">
    <div class="bg-banner box-border h-372px bg-#77775b pt-55px">
      <div class="mx-auto box-border w-1200px flex flex-col items-center text-center font-500">
        <p
          class="mb-6 text-32px text-white color-#EEEFD8 leading-16"
          :class="[
            {
              '!text-26px': commonStore.currentLocale === 'ja',
            },
          ]"
        >
          {{ $t('title1') }}
        </p>
        <p
          class="mx-auto mb-6 h-96px whitespace-pre-line text-16px text-white/60 color-#D5D6BE leading-8"
          :class="[
            {
              // '!text-26px': commonStore.currentLocale === 'ja',
              // '!px-200px': commonStore.currentLocale === 'ja',
              // '!text-center': commonStore.currentLocale === 'en',
            },
          ]"
        >
          {{ $t('title2') }}
        </p>
        <div
          class="h-54px w-177px cursor-pointer rounded-10px bg-#F9FBC2 text-center line-height-54px"
          @click="toPricePage"
        >
          {{ $t('免费试用') }}
        </div>
      </div>
    </div>
    <div class="relative">
      <div
        class="t-0 l-0 [clip-path:ellipse(65%_100%_at_50%_0%)] absolute h-145px w-full bg-#77775b"
      ></div>
      <video-thumbnail-list class="mb-40px -top-12px" @openVideo="handleOpenVideo" />

      <!-- <div class="font-#221C02 pb-35px pt-80px text-center text-52px">
        {{ $t('关于EduRPOlive') }}
      </div> -->
      <div
        id="swiper-x"
        class="relative z-2 mx-auto h-560px w-990px flex items-center justify-center rounded-8px bg-#383737"
      >
        <div class="h-550px w-980px rounded-8px">
          <swiper
            :modules="[Autoplay]"
            @swiper="setSwiper"
            :autoplay="swiperOptions.autoplay"
            class="h-full rounded-8px"
            @slideChange="onSlideChange"
          >
            <swiper-slide v-for="(item, index) in slideList" :key="index">
              <div
                class="relative box-border h-full flex flex-col items-center overflow-hidden py-0"
              >
                <div
                  class="absolute right-20px top-20px box-border h-40px w-40px flex scale-100 cursor-pointer items-center justify-center rounded-sm bg-#221C02 pl-6px color-white transition hover:scale-110 !text-4xl"
                  v-if="!visibleCategoryModal"
                  @click="handleToggleCategoryModal"
                >
                  <div class="i-fluent-mdl2-bulleted-list-text-mirrored"></div>
                </div>
                <Transition name="modal-fade">
                  <div
                    id="content-category-modal"
                    v-if="visibleCategoryModal"
                    class="absolute bottom-0 left-0 right-0 top-0 m-auto box-border color-white"
                  >
                    <div
                      class="absolute bottom-0 left-0 right-0 top-0 m-auto bg-black opacity-80"
                    ></div>
                    <div class="absolute bottom-0 left-0 right-0 top-0 z-2 m-auto p-20px">
                      <div class="text-right !text-2xl">
                        <el-icon class="cursor-pointer" @click="handleToggleCategoryModal">
                          <Close />
                        </el-icon>
                      </div>
                      <div class="image-select-wrap">
                        <div
                          v-for="item in categoryList"
                          :key="item.id"
                          class="image-select-item"
                          @click="handleCategoryClick(item.id)"
                        >
                          {{ item.name }}
                        </div>
                      </div>
                    </div>
                  </div>
                </Transition>
                <img :src="item.image" class="block h-full w-full" />
                <!-- <div -->
                <!--   class="absolute bottom-0 left-0 right-0 m-auto flex items-center justify-center bg-black text-center text-sm color-white line-height-30px opacity-60" -->
                <!--   v-show="textIndex === activeTextIndex" -->
                <!--   v-for="(text, textIndex) in item.texts" -->
                <!--   :key="textIndex" -->
                <!-- > -->
                <!--   <span> -->
                <!--     {{ text.content }} -->
                <!--   </span> -->
                <!-- </div> -->
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <div class="font-#221C02 flex justify-center py-18px font-500">
        <div
          v-for="(text, textIndex) in slideList[activeSlideIndex].texts"
          v-show="textIndex === activeTextIndex"
          :key="textIndex"
        >
          "{{ text.content }}"
        </div>
      </div>
    </div>
    <div class="font-#221C02 mx-auto max-w-1200px py-20px text-center text-14px font-500">
      <i18n-t keypath="title4" tag="span">
        <span class="cursor-pointer text-indigo" @click="toPricePage">
          {{ $t('title4Inset') }}
        </span>
      </i18n-t>
    </div>
    <!-- <div class="font-#221C02 mx-auto max-w-1200px py-20px text-center text-14px font-500">
      <i18n-t keypath="title5" tag="span">
        <span class="cursor-pointer text-crete" @click="toPricePage">
          {{ $t('title5Inset') }}
        </span>
      </i18n-t>
    </div> -->
    <div class="mt-60px">
      <p class="mb-56px text-center text-52px font-bold">
        {{ $t('downloadApp') }}
      </p>
      <div class="flex justify-center gap-50px">
        <a
          href="https://apps.apple.com/app/idxxxxx"
          target="_blank"
          rel="noopener noreferrer"
          class="h-60px flex items-center justify-center rounded-10px bg-#221c02 px-29px text-22px color-#f9fbc2 no-underline"
        >
          <img class="mr-10px h-15px w-15px" src="@/assets/images/down-arrow.png" />
          {{ $t('getEduLoopForIos') }}
        </a>
        <a
          href="https://apps.apple.com/app/idyyyyy"
          target="_blank"
          rel="noopener noreferrer"
          class="h-60px flex items-center justify-center rounded-10px bg-#221c02 px-29px text-22px color-#f9fbc2 no-underline"
        >
          <img class="mr-10px h-15px w-15px" src="@/assets/images/down-arrow.png" />
          {{ $t('getEduNumbusForIos') }}
        </a>
      </div>
    </div>
  </div>
  <fixedSidebar />
</template>
<script setup>
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Autoplay } from 'swiper/modules';
  import 'swiper/less';
  import 'swiper/less/pagination';
  import 'swiper/less/autoplay';
  import i18n from '@/i18n';
  // import swiperBox from '@/components/home/<USER>';

  import { ref, computed, watch, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useCommonStore } from '@/stores/common';
  import fixedSidebar from '@/components/home/<USER>';
  import videoThumbnailList from '@/components/home/<USER>';
  import { imageData } from './swiperList.js';

  const router = useRouter();

  const visibleCategoryModal = ref(false);
  const handleToggleCategoryModal = () => {
    visibleCategoryModal.value = !visibleCategoryModal.value;
    if (visibleCategoryModal.value) {
      mySwiper.value.autoplay.pause();
    } else {
      mySwiper.value.autoplay.resume();
    }
  };
  const categoryList = [
    { id: 'All', name: i18n.t('全部') },
    { id: 'Resources', name: i18n.t('resources.资源管理') },
    { id: 'Academics', name: i18n.t('academics.教务管理') },
    { id: 'Customers', name: i18n.t('customers.客户管理') },
    { id: 'Marketing', name: i18n.t('marketing.营销管理') },
    { id: 'SystemSettings', name: i18n.t('systemSetup.系统设置') },
  ];
  const handleCategoryClick = (category) => {
    console.log('Selected category:', category);
    visibleCategoryModal.value = false;

    slideList.value = category === 'All' ? slides : imageData[category];
  };

  const commonStore = useCommonStore();

  const toPricePage = () => {
    router.push({
      name: 'price',
    });
  };

  const slides = Object.values(imageData).flat();
  console.log('slides[0].imageDuration------', slides, slides[0]);
  const swiperOptions = computed(() => {
    return {
      autoplay: {
        delay: slides[0].imageDuration, // 初始延迟
        disableOnInteraction: false,
      },
    };
  });

  /**
   * 使用 swiper 组件的自定义 hook
   *
   * @param slideList swiper 组件的 tab 数据
   * @returns 包含 swiper 实例和设置 swiper 实例方法的对象
   */
  const useSwiper = ({ slideList, activeTextIndex, activeSlideIndex }) => {
    /**
     *  问题:在swiper组件上使用ref="mySwiper"会导致获取的swiper实例上的所有方法都是undefined,
     *  使用swiper组件的@swiper获取,获取的实例可以调用方法
     */

    const mySwiper = ref(null);
    /**
     * 设置 swiper 对象, 比ref获取更方便
     * @param swiper swiper 对象
     */
    const setSwiper = (swiper) => {
      mySwiper.value = swiper;
    };

    watch(slideList, async () => {
      activeTextIndex.value = 0; // 当前显示的文案索引
      activeSlideIndex.value = 0; // 当前图片索引
      await nextTick();
      const swiper = mySwiper.value;
      swiper?.update?.();
      swiper?.slideTo?.(0, 0);
      swiper?.autoplay?.resume?.();
    });
    return {
      mySwiper,
      setSwiper,
    };
  };

  const activeTextIndex = ref(0); // 当前显示的文案索引
  const activeSlideIndex = ref(0); // 当前图片索引
  const slideList = ref(slides);
  const { setSwiper, mySwiper } = useSwiper({ slideList, activeTextIndex, activeSlideIndex });

  // 文案轮播
  const startTextRotation = (slide) => {
    let currentIndex = 0;
    let timeoutId = null;

    const rotateText = () => {
      if (currentIndex < slide.texts.length) {
        activeTextIndex.value = currentIndex;
        // 每次设置新定时器前清除旧的
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(rotateText, slide.texts[currentIndex].duration);
        currentIndex++;
      }
    };

    rotateText();
  };

  const onSlideChange = (swiper, ...rest) => {
    const currentSlideIndex = swiper.activeIndex;
    activeSlideIndex.value = currentSlideIndex; // 设置当前图片索引
    const currentSlide = slideList.value[currentSlideIndex];
    activeTextIndex.value = 0; // 重置文案索引
    swiper.autoplay.stop(); // 停止自动播放
    swiper.autoplay.start(); // 重新启动自动播放，使用新的 delay
    swiper.params.autoplay.delay = currentSlide.imageDuration;
    startTextRotation(currentSlide);
  };
</script>
<style lang="less" scoped>
  /* 默认背景图像（适用于非高分屏设备） */
  .bg-banner {
    background: #77775b url('@/assets/images/banner.png') center center/cover no-repeat;
    background-size: 125%;
  }

  .image-select-wrap {
    margin-top: 10px;
    display: grid;
    grid-template-columns: 1fr; /* 单列布局 */
    gap: 15px; /* 保持原有的 gap */
  }
  .image-select-item {
    display: flex;
    width: 100%; /* 让所有子项宽度一致 */
    cursor: pointer;
    min-width: max-content;
    align-items: center;
    font-size: 16px;
    line-height: 24px;
    justify-content: center;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    border-style: solid;
    padding: 5px;
    &:hover {
      background-color: white;
      color: black;
    }
  }

  /* 模态窗口动画 */
  .modal-fade-enter-active,
  .modal-fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .modal-fade-enter-from,
  .modal-fade-leave-to {
    opacity: 0;
  }

  .modal-fade-enter-to,
  .modal-fade-leave-from {
    opacity: 1;
  }
</style>
