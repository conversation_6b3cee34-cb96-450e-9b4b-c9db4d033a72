import { loadImagesFromDirectory, getImagesBySubdirectory } from '@/utils/imageLoader';
import i18n from '@/i18n';

const oliveImages = loadImagesFromDirectory('/src/assets/images/new-home/olive');
export const imageData = {
  Resources: [
    {
      image: oliveImages['/Resources/1.png'],
      id: '1',
      imageDuration: 7000,
      texts: [
        { content: i18n.t('resources.组织结构'), duration: 3000 },
        { content: i18n.t('resources.EduRP提供了工具来映射您的运营层次结构。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Resources/2.png'],
      id: '2',
      imageDuration: 12000,
      texts: [
        {
          content: i18n.t('resources.通过组织管理模块，您可以定义部门并分配经理。'),
          duration: 4000,
        },
        { content: i18n.t('resources.并将每个部门标记为成本中心或利润中心。'), duration: 4000 },
        { content: i18n.t('resources.这确保了与会计和商业智能功能的无缝集成。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Resources/3.png'],
      id: '3',
      imageDuration: 3000,
      texts: [{ content: i18n.t('resources.员工管理'), duration: 3000 }],
    },
    {
      image: oliveImages['/Resources/4.png'],
      id: '4',
      imageDuration: 4000,
      texts: [{ content: i18n.t('resources.员工管理模块让您随时掌握员工信息。'), duration: 4000 }],
    },
    {
      image: oliveImages['/Resources/5.png'],
      id: '5',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('resources.管理详细的员工档案，包括个人和雇佣信息'), duration: 4000 },
        { content: i18n.t('resources.以及安全级别和角色等级。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Resources/6.png'],
      id: '6',
      imageDuration: 4000,
      texts: [{ content: i18n.t('resources.内置访问控制，确保为员工分配'), duration: 4000 }],
    },
    {
      image: oliveImages['/Resources/7.png'],
      id: '7',
      imageDuration: 4000,
      texts: [{ content: i18n.t('resources.与其职责相匹配的适当角色和权限。'), duration: 4000 }],
    },
    {
      image: oliveImages['/Resources/8.png'],
      id: '8',
      imageDuration: 4000,
      texts: [{ content: i18n.t('resources.赋能组织高效管理教职工和教室。'), duration: 4000 }],
    },
    {
      image: oliveImages['/Resources/9.png'],
      id: '9',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('resources.捕捉教师能力以实现与'), duration: 4000 },
        { content: i18n.t('resources.资源规划和排课的无缝集成。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Resources/10.png'],
      id: '10',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('resources.在教室管理方面，EduRP提供了'), duration: 4000 },
        { content: i18n.t('resources.用户友好的界面来管理教室。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Resources/11.png'],
      id: '11',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('resources.与教师管理机制类似，EduRP捕捉教室容量和'), duration: 4000 },
        { content: i18n.t('resources.功能以支持资源规划和排课的无缝集成。'), duration: 4000 },
      ],
    },
  ],
  Customers: [
    {
      image: oliveImages['/Customers/1.png'],
      id: '1',
      imageDuration: 7000,
      texts: [
        { content: i18n.t('customers.客户管理'), duration: 3000 },
        {
          content: i18n.t('customers.快速访问、筛选和管理客户档案，并提供全面的信息。'),
          duration: 4000,
        },
      ],
    },
    {
      image: oliveImages['/Customers/2.png'],
      id: '2',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('customers.家庭和学生的详细记录'), duration: 4000 },
        { content: i18n.t('customers.维护详细的家庭和学生档案'), duration: 4000 },
        { content: i18n.t('customers.包括入学历史，实现无缝管理。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/3.png'],
      id: '3',
      imageDuration: 7000,
      texts: [
        { content: i18n.t('customers.集成的购买记录'), duration: 3000 },
        { content: i18n.t('customers.直接在客户档案页面中记录会籍和课程购买。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/4.png'],
      id: '4',
      imageDuration: 3000,
      texts: [{ content: i18n.t('customers.简化的付款记录'), duration: 3000 }],
    },
    {
      image: oliveImages['/Customers/5.png'],
      id: '5',
      imageDuration: 4000,
      texts: [
        { content: i18n.t('customers.无缝记录客户付款以支持高级会计和BI功能。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/6.png'],
      id: '6',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('customers.准确的付款追踪'), duration: 4000 },
        {
          content: i18n.t('customers.确保通过详细的未付款余额和交易信息实现精准的付款追踪。'),
          duration: 4000,
        },
        { content: i18n.t('customers.在系统中轻松管理付款记录历史。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/7.png'],
      id: '7',
      imageDuration: 3000,
      texts: [{ content: i18n.t('customers.课程购买记录'), duration: 3000 }],
    },
    {
      image: oliveImages['/Customers/8.png'],
      id: '8',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('customers.轻松记录课程购买，并将其'), duration: 4000 },
        { content: i18n.t('customers.无缝集成到会计和BI系统中。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/9.png'],
      id: '9',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('customers.会籍一目了然'), duration: 4000 },
        { content: i18n.t('customers.客户服务人员可以查看客户的会籍。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/10.png'],
      id: '10',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('customers.全面的订单管理'), duration: 4000 },
        { content: i18n.t('customers.客户通过客户应用下单购买会籍和课程。'), duration: 4000 },
        { content: i18n.t('customers.订单管理使客户服务和销售团队能够查看'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/11.png'],
      id: '11',
      imageDuration: 4000,
      texts: [{ content: i18n.t('customers.并方便地管理客户订单。'), duration: 4000 }],
    },
    {
      image: oliveImages['/Customers/12.png'],
      id: '12',
      imageDuration: 4000,
      texts: [{ content: i18n.t('customers.轻松访问学生记录'), duration: 4000 }],
    },
    {
      image: oliveImages['/Customers/13.png'],
      id: '13',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('customers.追踪学习历史并访问出勤记录'), duration: 4000 },
        { content: i18n.t('customers.以提供全面的客户支持。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/14.png'],
      id: '14',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('customers.订单和会籍的统一概览'), duration: 4000 },
        { content: i18n.t('customers.除了客户特定的视图外，系统还提供'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Customers/15.png'],
      id: '15',
      imageDuration: 4000,
      texts: [{ content: i18n.t('customers.关于订单和会籍的全面视角。'), duration: 4000 }],
    },
  ],
  Academics: [
    {
      image: oliveImages['/Academics/1.png'],
      id: '1',
      imageDuration: 3000,
      texts: [{ content: i18n.t('academics.校历管理'), duration: 3000 }],
    },
    {
      image: oliveImages['/Academics/2.png'],
      id: '2',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('academics.轻松管理校历以简化教务管理操作'), duration: 4000 },
        { content: i18n.t('academics.并确保排课清晰明了。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/3.png'],
      id: '3',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('academics.定义并管理包含假期和学期安排的校历'), duration: 4000 },
        {
          content: i18n.t('academics.以确保有序的教务管理规划并促进精确的资源分配。'),
          duration: 4000,
        },
      ],
    },
    {
      image: oliveImages['/Academics/4.png'],
      id: '4',
      imageDuration: 3000,
      texts: [{ content: i18n.t('academics.学期管理'), duration: 3000 }],
    },
    {
      image: oliveImages['/Academics/5.png'],
      id: '5',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('academics.定义学期以简化基于学期的课程安排'), duration: 4000 },
        { content: i18n.t('academics.并维护教务管理结构。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/6.png'],
      id: '6',
      imageDuration: 15000,
      texts: [
        { content: i18n.t('academics.课程模板管理'), duration: 3000 },
        { content: i18n.t('academics.课程模板是教务管理运营的基础。'), duration: 4000 },
        { content: i18n.t('academics.它将教师和教室能力关联起来'), duration: 4000 },
        { content: i18n.t('academics.确保资源与课程需求一致。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/7.png'],
      id: '7',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('academics.课程模板还定义课程时长'), duration: 4000 },
        { content: i18n.t('academics.并与定价类别相关联'), duration: 4000 },
        { content: i18n.t('academics.实现与定价策略的无缝整合。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/8.png'],
      id: '8',
      imageDuration: 3000,
      texts: [{ content: i18n.t('academics.课程分类管理'), duration: 3000 }],
    },
    {
      image: oliveImages['/Academics/9.png'],
      id: '9',
      imageDuration: 4000,
      texts: [
        {
          content: i18n.t('academics.轻松将课程组织为层次结构以分组类似的课程模板'),
          duration: 4000,
        },
      ],
    },
    {
      image: oliveImages['/Academics/10.png'],
      id: '10',
      imageDuration: 4000,
      texts: [
        {
          content: i18n.t('academics.将其归入更广泛的类别，例如团体课程或私人课程。'),
          duration: 4000,
        },
      ],
    },
    {
      image: oliveImages['/Academics/11.png'],
      id: '11',
      imageDuration: 4000,
      texts: [{ content: i18n.t('academics.班级管理'), duration: 4000 }],
    },
    {
      image: oliveImages['/Academics/12.png'],
      id: '12',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('academics.班级的添加和发布是资源分配中的关键操作'), duration: 4000 },
        { content: i18n.t('academics.支持顺畅的教务管理运营。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/13.png'],
      id: '13',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('academics.基于模板的排课'), duration: 4000 },
        { content: i18n.t('academics.通过首先选择预定义的课程模板来安排课程'), duration: 4000 },
        { content: i18n.t('academics.以确保与资源能力的兼容性。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/14.png'],
      id: '14',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('academics.资源分配'), duration: 4000 },
        { content: i18n.t('academics.为课程分配合适的教师和教室资源'), duration: 4000 },
        { content: i18n.t('academics.确保资源的最佳利用。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/15.png'],
      id: '15',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('academics.排课确认后'), duration: 4000 },
        {
          content: i18n.t('academics.可以在「排课详情」部分查看课程安排，以便清晰跟踪。'),
          duration: 4000,
        },
      ],
    },
    {
      image: oliveImages['/Academics/16.png'],
      id: '16',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('academics.调整资源分配'), duration: 4000 },
        { content: i18n.t('academics.排课确认后，可以查看并优化资源分配。'), duration: 4000 },
        { content: i18n.t('academics.课程发布后仍支持编辑，确保灵活性。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Academics/17.png'],
      id: '17',
      imageDuration: 15000,
      texts: [
        { content: i18n.t('academics.考勤详情'), duration: 3000 },
        { content: i18n.t('academics.查看通过教师移动应用记录的学生出勤详情。'), duration: 4000 },
        { content: i18n.t('academics.对于缺勤或有争议的出勤记录'), duration: 4000 },
        { content: i18n.t('academics.可以在系统中进行必要的调整。'), duration: 4000 },
      ],
    },
  ],
  Marketing: [
    {
      image: oliveImages['/Marketing/1.png'],
      id: '1',
      imageDuration: 7000,
      texts: [
        { content: i18n.t('marketing.定价类型'), duration: 3000 },
        {
          content: i18n.t('marketing.定价类型有效地将定价和产品分离, 让定价更灵活。'),
          duration: 4000,
        },
      ],
    },
    {
      image: oliveImages['/Marketing/2.png'],
      id: '2',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('marketing.价格表'), duration: 4000 },
        { content: i18n.t('marketing.“标价表”用于标准定价。'), duration: 4000 },
        { content: i18n.t('marketing.“优惠价格表”用于管理促销定价策略。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Marketing/3.png'],
      id: '3',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('marketing.每个校区只能有一个「标价表」。'), duration: 4000 },
        { content: i18n.t('marketing.校区必须有「标价表」，以促进有效的课程销售'), duration: 4000 },
        { content: i18n.t('marketing.和资源规划。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Marketing/4.png'],
      id: '4',
      imageDuration: 4000,
      texts: [
        { content: i18n.t('marketing.「优惠价格表」与特定的会籍和促销相关联。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Marketing/5.png'],
      id: '5',
      imageDuration: 4000,
      texts: [{ content: i18n.t('marketing.这允许为会员提供优惠价。'), duration: 4000 }],
    },
    {
      image: oliveImages['/Marketing/6.png'],
      id: '6',
      imageDuration: 3000,
      texts: [{ content: i18n.t('marketing.会籍设置'), duration: 3000 }],
    },
    {
      image: oliveImages['/Marketing/7.png'],
      id: '7',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('marketing.灵活的会籍定义'), duration: 4000 },
        { content: i18n.t('marketing.EduRP允许用户定义灵活的会籍类型，'), duration: 4000 },
        { content: i18n.t('marketing.以满足培训中心的不同促销和客户需求。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Marketing/8.png'],
      id: '8',
      imageDuration: 4000,
      texts: [
        { content: i18n.t('marketing.高级配置支持将会员资格链接到优惠价格表。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Marketing/9.png'],
      id: '9',
      imageDuration: 12000,
      texts: [
        { content: i18n.t('marketing.促销管理'), duration: 4000 },
        { content: i18n.t('marketing.系统支持定义和管理优惠券和促销折扣，'), duration: 4000 },
        { content: i18n.t('marketing.以增强营销活动并吸引客户。'), duration: 4000 },
      ],
    },
    {
      image: oliveImages['/Marketing/10.png'],
      id: '10',
      imageDuration: 15000,
      texts: [
        { content: i18n.t('marketing.EduRP支持三种不同的促销生成方法：'), duration: 3000 },
        {
          content: i18n.t('marketing.1. 按条件生成: 适用类似于“买一送一”的优惠。'),
          duration: 4000,
        },
        { content: i18n.t('marketing.2. 自动生成: 自动生成促销，例如免费试用。'), duration: 4000 },
        {
          content: i18n.t('marketing.3. 手动生成: 针对特定促销活动手动派发的优惠券。'),
          duration: 4000,
        },
      ],
    },
  ],
  SystemSettings: [
    {
      image: oliveImages['/SystemSetup/1.png'],
      id: '1',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('systemSetup.角色管理与权限'), duration: 4000 },
        {
          content: i18n.t('systemSetup.查看已定义角色及其描述，并管理角色配置。'),
          duration: 4000,
        },
      ],
    },
    {
      image: oliveImages['/SystemSetup/2.png'],
      id: '2',
      imageDuration: 8000,
      texts: [
        { content: i18n.t('systemSetup.轻松设置和编辑角色，同时分配细粒度权限'), duration: 4000 },
        { content: i18n.t('systemSetup.以控制对不同系统功能的访问。'), duration: 4000 },
      ],
    },
  ],
};
