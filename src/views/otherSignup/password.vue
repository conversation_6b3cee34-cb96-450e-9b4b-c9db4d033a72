<template>
  <bg-container>
    <div class="box-border flex flex-1 justify-center pt-80px">
      <div class="olive1-sign-x" v-if="oliveParentSubmited">
        <div class="olive1-content">
          <p class="icon-x">
            <el-icon class="olive1-success__icon"><CircleCheck /></el-icon>
          </p>
          <div class="olive1-success__title">
            {{ $t('家长注册成功') }}
          </div>
        </div>
      </div>
      <div class="password-reset" v-else>
        <div class="flex gap-60px">
          <div class="flex-1 text-34px font-bold">{{ $t('创建密码') }}</div>
          <div class="flex-1">
            <passwordWithStrength
              ref="passwordWithStrengthRef"
              :pForm="form"
            ></passwordWithStrength>
            <el-button
              type="primary"
              size="default"
              class="submit-btn-default"
              @click="handleSubmitPassword"
              v-on:keyup.enter="handleSubmitPassword"
            >
              {{ $t('提交') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </bg-container>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { ElMessage } from 'element-plus';
  import APICustomer from '@/service/APIcustomer.js';
  import i18n from '@/i18n';
  //  passwordWithStrength 会修改 form 中的password和confirmPassword
  import passwordWithStrength from '@/components/passwordWithStrength.vue';
  import { useRouteData } from '@/hooks/useRouteData';

  // 路由相关
  const { query, route } = useRouteData();
  // 默认角色类型 员工2 家长1
  const defaultRoleType = computed(() => (route.name === 'signupB2cPassword' ? 1 : 2));
  const appCode = computed(() => query.value.appCode || acceptResult.value.appCode || 'green');
  // console.log('defaultRoleType-===-', defaultRoleType.value);
  // console.log('appCode-----', appCode.value);
  // 机构版家长
  const isOliveParent = appCode.value + defaultRoleType.value == 'olive1';
  // 机构版家长提交成功
  const oliveParentSubmited = ref(false);

  // 表单相关
  const form = reactive({
    password: '',
    confirmPassword: '',
  });
  const passwordWithStrengthRef = ref(null);

  // 初始化数据
  const acceptResult = ref({});
  const customerId = computed(() => {
    return acceptResult.value.customerId;
  });

  const initData = async () => {
    try {
      const result = await APICustomer.signupAcceptAPI(`${appCode.value}/user`, {
        data: {
          activationToken: query.value.token,
        },
        externalHeaders: {
          'default-role-type': defaultRoleType.value,
        },
      });
      if (result) {
        if (result.body) {
          acceptResult.value = result.body;
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  onMounted(initData);

  // 提交密码
  const handleSubmitPassword = async () => {
    try {
      // 1. 密码验证组件的submitForm方法返回验证结果
      await passwordWithStrengthRef.value.submitForm();
      // 2. 验证通过后，进入系统登录页面
      submitHandle();
    } catch (error) {
      console.error('error------', error);
    }
  };

  const submitHandle = async () => {
    try {
      const result = await APICustomer.signupActivateAPI(`${appCode.value}/user`, {
        data: {
          ...acceptResult.value,
          password: form.password,
        },
        externalHeaders: {
          'default-role-type': defaultRoleType.value,
        },
      });
      if (result?.body) {
        ElMessage.success(i18n.t('提交成功'));
        if (isOliveParent) {
          // 机构版家长，暂时不跳转，未来应该要引导下载app
          oliveParentSubmited.value = true;
        } else {
          // 其他角色
          goPage(result.body);
        }
      } else {
        ElMessage.error(i18n.t('提交失败'));
      }
    } catch (error) {
      console.error('error-----', error);
    }
  };

  // 提交成功跳转
  const goPage = async (body = {}) => {
    const urlObj = {
      green1: import.meta.env.VITE_GREEN_PARENT_URL, // Green家长
      green2: import.meta.env.VITE_GREEN_URL, // Green员工
      olive: import.meta.env.VITE_OLIVE_URL, // Olive员工
      mto: import.meta.env.VITE_MTO_URL, // MTO员工
    };

    const key =
      appCode.value === 'green'
        ? `${appCode.value}${acceptResult.value.defaultRoleType}`
        : appCode.value;

    let baseUrl = urlObj[key];

    const url = `${baseUrl}/login?sgtoken=${body.tokenType} ${body.token}&customerId=${customerId.value}`;
    console.log('url----', url);
    // TODO: 打开新页面，正式要在当前页面打开
    window.open(url, '_blank');
  };
</script>

<style lang="less" scoped>
  @green: #58c0ad;
  @red: #e36560;

  .olive1-success__icon {
    font-size: 64px;
    color: #72c040;
    margin-bottom: 20px;
  }

  .olive1-success__title {
    font-size: 24px;
    margin: 20px 0;
    color: #333;
    text-align: center;
    line-height: 40px;
  }

  .olive1-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .olive1-x {
    height: 260px;
    display: flex;
    align-items: center;
  }
</style>
