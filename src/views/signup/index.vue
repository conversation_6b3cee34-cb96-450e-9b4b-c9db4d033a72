<template>
  <bg-container>
    <signup v-if="stepName === 'index'" :query="query"></signup>
    <notification v-if="stepName === 'notification'" :query="query"></notification>
    <password v-if="stepName === 'password'" :query="query"></password>
    <info v-if="stepName === 'info'" :query="query"></info>
  </bg-container>
</template>

<script setup>
  import { useRoute } from 'vue-router';
  import { computed } from 'vue';
  import selectLocale from '@/components/select-locale';
  import signup from './coms/signup.vue'; // 1
  import notification from './coms/notification.vue'; // 2
  import password from './coms/password.vue'; // 3
  import info from './coms/info.vue'; // 4
  const route = useRoute();

  const stepName = computed(() => {
    return route.params.stepName;
  });

  const query = computed(() => {
    const result = {};
    const query = route.query;
    for (const key in query) {
      result[key] = decodeURIComponent(query[key]);
    }
    return result;
  });

  defineOptions({
    beforeRouteEnter: (to, from, next) => {
      const validStepNames = ['index', 'notification', 'password', 'info'];

      if (validStepNames.includes(to.params.stepName)) {
        next();
      } else {
        // 访问到不存在的stepName,跳转到404页面
        next({ path: '/signup/index' });
      }
    },
  });
</script>

<style lang="less" scoped>
  .signup-view {
    background-color: #221c02;
    height: 100vh;
    position: relative;
  }
  .signup-view::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 380px;
  }
  .signup-content {
    position: relative;
    z-index: 1;
    height: 100%;
  }
  .select-locale-x {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    height: 80px;
    padding-right: 20px;
    .select-locale {
      color: var(--sign-color-white);
      font-size: 18px;
    }
  }
  .box {
    position: absolute;
    box-sizing: border-box;
    width: 508px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding-bottom: 64px;
    padding-top: 40px;
    padding-left: 40px;
    padding-right: 40px;
    background-color: #fff;
    filter: drop-shadow(0 20px 13px rgb(0 0 0 / 0.03)) drop-shadow(0 8px 5px rgb(0 0 0 / 0.08));
  }
  .logo {
    position: absolute;
    top: -153px;
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
  }
  .bottom-text {
    position: absolute;
    bottom: -60px;
    left: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--sign-color-gray);

    &::before {
      margin-right: 15px;
    }
    &::after {
      margin-left: 15px;
    }
    &::before,
    &::after {
      display: inline-block;
      content: '';
      width: 40px;
      height: 1px;
      background: var(--sign-color-gray);
    }
  }
</style>
