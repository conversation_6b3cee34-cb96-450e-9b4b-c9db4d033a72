import i18n from '@/i18n';
// import { isEmail } from '@/utils/validators.js'
// const checkEmail = (rule, value, callback) => {
//   if (!value && rule.required) {
//     callback(new Error(i18n.t('请输入正确的邮箱地址')))
//   } else if (!value) {
//     callback()
//   } else if (!isEmail(value)) {
//     callback(new Error(i18n.t('请输入正确的邮箱地址')))
//   } else {
//     callback()
//   }
// }
export const formConfig = {
  formItems: [
    {
      field: 'orgName',
      prop: 'orgName',
      label: i18n.t('公司名称'),
      type: 'input',
      placeholder: i18n.t('请输入公司名称'),
      rules: [{ required: true, message: i18n.t('请输入公司名称'), trigger: 'blur' }],
    },
    // {
    //   field: 'phone',
    //   prop: 'phone',
    //   type: 'phone',
    //   label: i18n.t('手机号'),
    //   otherOptions: {
    //     'auto-complete': 'new-password',
    //   },
    // },
    // {
    //   field: 'phoneCountry',
    //   prop: 'phoneCountry',
    //   hidden: true,
    // },
    // {
    //   field: 'nationality',
    //   prop: 'nationality',
    //   type: 'select',
    //   label: i18n.t('国家'),
    //   placeholder: i18n.t('请选择国家'),
    //   rules: [
    //     {
    //       required: true,
    //       message: i18n.t('请选择国家'),
    //       trigger: ['change', 'blur'],
    //     },
    //   ],
    // },
  ],
  colLayout: {
    span: 24,
  },
  labelPosition: 'top',
};
