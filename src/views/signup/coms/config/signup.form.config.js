import { isEmail } from '@/utils/validators.js';
import i18n from '@/i18n';
const checkEmail = (rule, value, callback) => {
  if (!value && rule.required) {
    callback(new Error(i18n.t('请输入正确的邮箱地址')));
  } else if (!value) {
    callback();
  } else if (!isEmail(value)) {
    callback(new Error(i18n.t('请输入正确的邮箱地址')));
  } else {
    callback();
  }
};
export const firstNameItem = {
  field: 'firstName',
  prop: 'firstName',
  label: i18n.t('名'),
  type: 'input',
  placeholder: i18n.t('请输入名字'),
  rules: [{ required: true, message: i18n.t('请输入名字'), trigger: 'blur' }],
};
export const lastNameItem = {
  field: 'lastName',
  prop: 'lastName',
  label: i18n.t('姓'),
  type: 'input',
  placeholder: i18n.t('请输入姓氏'),
  rules: [{ required: true, message: i18n.t('请输入姓氏'), trigger: 'blur' }],
};

export const middleNameItem = {
  field: 'middleName',
  prop: 'middleName',
  label: i18n.t('中间名'),
  type: 'input',
  placeholder: i18n.t('请输入中间名'),
};
export const formConfig = {
  formItems: [
    {
      field: 'email',
      prop: 'email',
      type: 'input',
      label: i18n.t('邮箱'),
      placeholder: i18n.t('请输入邮箱'),
      rules: [
        {
          required: true,
          message: i18n.t('请输入正确的邮箱地址'),
          trigger: ['blur', 'change'],
          validator: checkEmail,
        },
      ],
    },
  ],
  colLayout: {
    span: 24,
  },
  labelPosition: 'top',
};
