<template>
  <div class="flex gap-60px">
    <div class="flex-1 text-34px font-bold">{{ $t('创建密码') }}</div>
    <div class="flex-1">
      <passwordWithStrength ref="passwordWithStrengthRef" :pForm="form"></passwordWithStrength>
      <el-button
        type="primary"
        size="default"
        class="submit-btn-default"
        @click="handleSubmitPassword"
        v-on:keyup.enter="handleSubmitPassword"
      >
        {{ $t('提交') }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import i18n from '@/i18n';
  import passwordWithStrength from '@/components/passwordWithStrength.vue';
  import APICustomer from '@/service/APIcustomer.js';

  const props = defineProps({
    query: {},
  });

  // 表单数据（passwordWithStrength 会修改 form 中的 password 和 confirmPassword 字段）
  const form = reactive({
    password: '',
    confirmPassword: '',
  });

  const router = useRouter();
  const passwordWithStrengthRef = ref(null);

  const handleSubmitPassword = async () => {
    try {
      // 调用封装组件暴露出来的 handleSubmitPassword 方法进行验证
      await passwordWithStrengthRef.value.submitForm();
      sessionStorage.setItem(
        'signupAcceptData',
        JSON.stringify({
          ...dataObj.value,
          password: form.password,
        }),
      );
      // console.log('sessionStorage---', sessionStorage.getItem('signupAcceptData'));
      ElMessage.success(i18n.t('密码设置成功'));
      router.push({
        path: '/signup/info',
      });
    } catch (error) {
      console.error(error);
    }
  };

  const dataObj = ref({});
  const initData = async () => {
    try {
      const result = await APICustomer.signupAcceptAPI('mto', {
        data: {
          activationToken: props.query.token,
        },
        externalHeaders: {
          'customer-id': 'MTO',
          'default-role-type': 1,
        },
      });

      if (result) {
        if (result.body) {
          dataObj.value = result.body;
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  initData();
</script>

<style lang="less" scoped></style>
