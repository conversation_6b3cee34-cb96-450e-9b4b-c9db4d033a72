<template>
  <div class="panel-x">
    <div class="panel-left">
      {{ $t('创建Olivegreen账户') }}
    </div>
    <div class="panel-right">
      <div class="form-x">
        <baseForm
          ref="formRef"
          :value="form"
          v-bind="formConfigData"
          label-width="auto"
          @input="formDataValueChange"
        ></baseForm>
      </div>
      <div class="bottom-x">
        <div class="mb-20px">
          <input
            type="checkbox"
            id="agree"
            v-model="isAgree"
            class="agree-checkbox cursor-pointer align-middle"
          />
          <label for="agree" class="ml-10px text-14px color-#999999">
            <i18n-t keypath="agreeTemp">
              <router-link
                :to="{ name: 'termsService' }"
                class="color-#999999 underline"
                target="_blank"
              >
                {{ $t('服务条款') }}
              </router-link>
              <router-link
                :to="{ name: 'privacyPolicy' }"
                class="color-#999999 underline"
                target="_blank"
              >
                {{ $t('隐私政策') }}
              </router-link>
            </i18n-t>
          </label>
        </div>
        <el-button
          type="primary"
          size="default"
          class="submit-btn-default mt-20px"
          :disabled="disabledSubmit"
          @click="submitHandle()"
          v-on:keyup.enter="submitHandle()"
        >
          {{ $t('注册') }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
  import i18n from '@/i18n';
  import baseForm from '@/base-ui/baseForm/src/baseForm.vue';
  import APICustomer from '@/service/APIcustomer.js';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';
  import { formConfig, firstNameItem, lastNameItem } from './config/signup.form.config';

  const isAgree = ref(false);
  watch(isAgree, (value) => {
    console.log('isAgree---', value);
  });
  const props = defineProps({
    query: {},
  });

  const router = useRouter();
  const formConfigData = computed(() => {
    const config = {
      ...formConfig,
    };
    config.formItems = [lastNameItem, firstNameItem, ...config.formItems];
    return config;
  });

  const form = ref({
    firstName: '',
    lastName: '',
    // middleName: '',
    email: '',
  });
  // 如果form没填完或者没有同意协议，禁用提交按扭
  const disabledSubmit = computed(
    () => !(Object.values(form.value).every((field) => field?.trim()) && isAgree.value),
  );

  const locale = computed(() => {
    return i18n.getLocale();
  });
  const formDataValueChange = (value) => {
    form.value = value;
  };

  const submitAPI = () => {
    let person = {};
    if (locale.value === 'en') {
      person = {
        surnameWestern: form.value.lastName,
        givenNameWestern: form.value.firstName,
        // middleName: form.value.middleName,
        surnameOriental: '',
        givenNameOriental: '',
      };
    } else {
      person = {
        surnameOriental: form.value.lastName,
        givenNameOriental: form.value.firstName,
        surnameWestern: '',
        givenNameWestern: '',
        // middleName: '',
      };
    }
    const domain = import.meta.env.VITE_FRONTEND_URL;
    return APICustomer.signupApplyAPI({
      data: {
        username: form.value.email,
        usernameType: 2,
        domain: domain, // 注册都在官网上,所以直接用当前地址就可以.
        targetAppCode: props.query.code, // 'green' 'olive'
        paymentServicePriceId: props.query.priceId, // 价格id
        person,
      },
      externalHeaders: {
        'customer-id': 'MTO',
        'default-role-type': 1,
      },
    });
  };

  const toNo = () => {
    console.log('toNo');
    router.push(`/signup/notification`);
  };

  const formRef = ref(null);
  const submitHandle = async () => {
    formRef.value?.ruleForm?.validate(async (valid) => {
      if (valid) {
        if (!isAgree.value) {
          ElMessage.error(i18n.t('请阅读并接受服务条款和隐私政策'));
          return;
        }
        try {
          const result = await submitAPI();
          console.log('result---', result);

          if (result?.body) {
            sessionStorage.setItem('signupData', JSON.stringify({ email: form.value.email }));
            ElMessage.success(i18n.t('提交成功'));
            router.push(`/signup/notification?code=${props.query.code}`);
          } else {
            ElMessage.error(i18n.t('提交失败'));
          }
        } catch (error) {
          console.error('error-----', error);
        }
      }
    });
  };

  const handleKeyDown = (e) => {
    let key = e.keyCode !== undefined ? e.keyCode : window.event.keyCode;
    if (key === 13) {
      submitHandle();
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown);
  });

  onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeyDown);
  });
</script>

<style lang="less" scoped>
  .panel-x {
    display: flex;
    gap: 35px;
    .panel-left,
    .panel-right {
      flex: 1;
    }
  }
  .panel-left {
    font-weight: bold;
    font-size: 34px;
    color: #221c02;
  }
  .bottom-x {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 20px;
  }

  :deep {
    .el-form-item {
      margin-bottom: 0;
    }
    .el-form-item__label {
      padding: 0;
      color: #222222;
      font-size: 18px;
    }
    .el-input__inner {
      height: 54px;
      line-height: 54px;
    }
    .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
      display: none;
    }
    .el-input__wrapper {
      background-color: #eaebd0;
      border-color: #bababa;
    }
  }
  .agree-checkbox {
    appearance: none; /* 移除默认样式（兼容性较好） */
    -webkit-appearance: none; /* Safari/Chrome */
    -moz-appearance: none; /* Firefox */
    width: 14px; /* 自定义尺寸 */
    height: 14px;
    border: 2px solid #999; /* 边框颜色 */
    border-radius: 50%; /* 圆角 */
    background-color: #eaebd0; /* 默认背景色 */
    outline: none; /* 移除焦点轮廓 */
  }
  /* 选中时的样式 */
  .agree-checkbox:checked {
    background-color: #aaa; /* 选中后的背景色 */
  }

  /* 禁用状态（可选） */
  .agree-checkbox:disabled {
    background-color: #e0e0e0;
    border-color: #999;
    cursor: not-allowed;
  }
</style>
