<template>
  <div class="notification-x flex-1">
    <div class="signup-content">
      <p class="mb-15px text-center">
        <img src="@/assets/images/success.png" width="82px" height="101px" alt="" />
      </p>
      <p class="p1">
        {{ $t('t128') }}
      </p>
      <div class="p2">
        <p>
          {{ $t('t129') }}
        </p>
        <p>{{ email }}</p>
      </div>
      <p class="p3">
        {{ $t('t130') }}
        <a :href="'#/signup/index?code=' + query.code">{{ $t('t133') }}</a>
      </p>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  const props = defineProps({
    query: {},
  });
  const email = ref('');

  let signupData = sessionStorage.getItem('signupData');
  if (signupData) {
    signupData = JSON.parse(signupData);
    email.value = signupData.email;
  }
</script>

<style lang="less" scoped>
  .signup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .p1,
  .p2,
  .p3 {
    text-align: center;
    color: #a2a2a2;
    width: 320px;
    margin: 0 auto;
  }
  .p1 {
    margin-bottom: 10px;
    font-weight: bold;
    font-size: 34px;
    color: #221c02;
    line-height: 41px;
  }
  .p2 {
    margin-bottom: 25px;
    font-weight: 600;
    font-size: 18px;
    color: #221c02;
    line-height: 25px;
  }
  .p3 {
    word-break: break-word;
    a {
      color: #a2a2a2;
      &:hover {
        color: #777;
      }
    }
  }
</style>
