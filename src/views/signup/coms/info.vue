<template>
  <div class="panel-x">
    <div class="panel-left">
      <p class="title">{{ $t('欢迎') }}</p>
      <p class="info">{{ $t('t132') }}</p>
    </div>
    <div class="panel-right">
      <div class="form-x">
        <baseForm
          ref="formRef"
          :value="form"
          v-bind="formConfigData"
          label-width="auto"
          @input="formDataValueChange"
        ></baseForm>
      </div>
      <el-button
        type="primary"
        size="default"
        class="submit-btn-default"
        :disabled="!form.orgName"
        @click="submitHandle()"
        v-on:keyup.enter="submitHandle()"
      >
        {{ $t('开始') }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { ElMessage } from 'element-plus';
  import { useRouter, useRoute } from 'vue-router';
  import myStripe from '@/utils/stripe';
  import LocalCache from '@/utils/localCache';
  import APICustomer from '@/service/APIcustomer.js';
  import baseForm from '@/base-ui/baseForm/src/baseForm.vue';
  import { formConfig } from './config/info.form.config';
  import i18n from '@/i18n';

  const formRef = ref(null);
  const form = reactive({
    orgName: '',
  });
  const signupAcceptData = ref({});

  const formConfigData = computed(() => {
    const config = {
      ...formConfig,
    };

    return config;
  });

  const formDataValueChange = (value) => {
    Object.assign(form, value);
  };
  const currentLocaleName = LocalCache.getCache('locale')?.name || 'zh'; // zh ja en

  // 提交
  const submitHandle = () => {
    formRef.value?.ruleForm.validate(async (valid) => {
      if (valid) {
        try {
          const result = await signupActivateAPI();
          console.log('activate---result---', result);
          if (result?.body) {
            ElMessage.success(i18n.t('提交成功'));

            const targetAppCode = signupAcceptData.value?.targetAppCode;
            const body = result.body;

            // 构建token
            const sgtoken = `${body.tokenType} ${body.token}`;
            const customerId = body?.authzAccounts?.[0]?.customerId;

            // 应用跳转处理器
            const appHandlers = {
              // olive 跳转到stripe
              olive: () => goPage(body),
              green: () => {
                // green 直接跳转到系统
                const baseUrl = import.meta.env.VITE_GREEN_URL;
                const url = `${baseUrl}/login?sgtoken=${sgtoken}&customerId=${customerId}&lang=${currentLocaleName}`;
                location.href = url;
              },
            };

            // 执行对应应用的跳转逻辑
            const handler = appHandlers[targetAppCode];
            if (handler) {
              handler();
            }
          } else {
            ElMessage.error(i18n.t('提交失败'));
          }
        } catch (error) {
          console.error('error-----', error);
        }
      }
    });
  };

  // 提交成功跳转
  const goPage = async (body = {}) => {
    // 如果不需要跳转到stripe页面,直接登录
    const GREEN_URL = `${import.meta.env.VITE_GREEN_URL}`;
    const OLIVE_URL = `${import.meta.env.VITE_OLIVE_URL}`;
    const urlObj = {
      green: GREEN_URL,
      olive: OLIVE_URL,
    };
    const targetAppCode = signupAcceptData.value?.targetAppCode;
    const baseUrl = urlObj[targetAppCode];

    let url = await toCheckout({
      code: targetAppCode,
      sgtoken: `${body.tokenType} ${body.token}`,
      customerId: body?.authzAccounts?.[0]?.customerId,
      priceId: signupAcceptData.value.paymentServicePriceId,
    });
    // 测试能不能直接登录
    // url =
    //   'http://localhost:8083/#/login?code=olive&signup=1&priceId=111&sgtoken=Bearer%20eyJhbGciOiJIUzI1NiJ9.**********************************************************************************.TNTxp4vyB9pPZ5qJSAGJEUs5x5gzfD3p6-tDLjD3rTE&customerId=YIJIE_2017_FAKE';

    // url = `http://localhost:8083/#/checkout?code=olive&signup=1&priceId=${this.$t(
    //   'priceId.priceId88',
    // )}&sgtoken=Bearer%20eyJhbGciOiJIUzI1NiJ9.**********************************************************************************.TNTxp4vyB9pPZ5qJSAGJEUs5x5gzfD3p6-tDLjD3rTE&customerId=YIJIE_2017_FAKE`;

    // 测试获取url
    // url = await toCheckout({
    //   code: 'olive',
    //   sgtoken:
    //     'Bearer eyJhbGciOiJIUzI1NiJ9.**********************************************************************************.TNTxp4vyB9pPZ5qJSAGJEUs5x5gzfD3p6-tDLjD3rTE',
    //   customerId: 'YIJIE_2017_FAKE',
    //   priceId: 'price_1PnGdjJQ6NsEpmsCg0hKr5Wf'
    // })
    console.log('url-----', url);
    // location.href = url;
    // // TODO 方便注册测试订阅，正式需要改成location.herf。
    window.open(url, '_blank');
  };

  const signupActivateAPI = () => {
    return APICustomer.signupActivateAPI('mto', {
      data: {
        ...signupAcceptData.value,
        orgName: form.orgName,
      },
      externalHeaders: {
        'customer-id': 'MTO',
        'default-role-type': 1,
      },
    });
  };

  const toCheckout = async (obj) => {
    let url = '';
    const sgtoken = obj.sgtoken;
    const customerId = obj.customerId;
    const code = obj.code;
    // 设置token
    LocalCache.setCache('token', sgtoken || '');
    // 设置customerId
    LocalCache.setCache('customerId', customerId || '');
    // 设置code
    LocalCache.setCache('code', code || '');

    const trialPeriodDays = 30;
    const result = await myStripe.getCheckoutSession({
      priceId: obj.priceId,
      trialPeriodDays: trialPeriodDays,
      code: code,
      successUrlQuery: {
        code: code,
        signup: 1,
        sgtoken: sgtoken,
        customerId: customerId,
        priceId: obj.priceId,
        d: trialPeriodDays,
      },
      cancelUrlQuery: { signup: 1 },
    });
    // console.log('result?.body-----', result?.body)
    url = result?.body?.checkoutUrl;
    return url;
  };

  const initData = () => {
    let signupAcceptDataFromStorage = sessionStorage.getItem('signupAcceptData');
    signupAcceptDataFromStorage = signupAcceptDataFromStorage
      ? JSON.parse(signupAcceptDataFromStorage)
      : signupAcceptDataFromStorage;
    signupAcceptData.value = signupAcceptDataFromStorage;
  };

  initData();
</script>

<style lang="less" scoped>
  .panel-x {
    display: flex;
    gap: 35px;
    .panel-left,
    .panel-right {
      flex: 1;
    }
  }
  .panel-left {
    .title {
      font-size: 22px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .info {
      color: #818385;
      margin-bottom: 20px;
    }
  }

  :deep {
    .yj-form {
      padding: 0;
      .el-form-item {
        margin-bottom: 0;
        padding: 0 !important;
      }
      .el-form-item__label {
        padding: 0;
        color: #222222;
        font-size: 18px;
      }
      .el-input__inner {
        height: 54px;
        line-height: 54px;
      }
      .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
        display: none;
      }

      .el-input__wrapper {
        background-color: #eaebd0;
        border-color: #bababa;
      }
    }
  }
</style>
