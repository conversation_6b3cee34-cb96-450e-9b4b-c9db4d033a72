<template>
  <selectLocale class="right-20px top-20px text-18px color-#374151 !fixed"></selectLocale>
  <div>
    <div class="bg-#C0C1A4">
      <div class="mx-auto max-w-3xl px-4 py-8 container">
        <h1 class="mb-6 text-4xl font-semibold">{{ $t('privacyPolicy.title') }}</h1>
        <p class="mb-6 text-lg">
          <span class="mr-1">{{ $t('privacyPolicy.effectiveDate.title') }}:</span>
          <span class="font-semibold">{{ $t('privacyPolicy.effectiveDate.date') }}</span>
        </p>
        <p>
          <i18n-t keypath="privacyPolicy.introductionTemp">
            <span class="mr-1 font-semibold">
              {{ $t('privacyPolicy.OlivegreenLLC') }}
            </span>
            <span class="font-semibold">
              {{ $t('privacyPolicy.EduRP') }}
            </span>
          </i18n-t>
        </p>
      </div>
    </div>
    <div class="mx-auto max-w-3xl px-4 py-8 container">
      <section v-for="(section, index) in sections" :key="index" class="mb-6">
        <template v-if="[0, 7].includes(index)">
          <h2 class="mb-4 text-2xl font-semibold">{{ section.title }}</h2>
          <div>
            <p>{{ section.content.title }}</p>
            <ul v-if="section.content.content" class="pt-20px">
              <li v-for="(item, i) in section.content.content" :key="i" class="not-last:mb-20px">
                <p class="mb-2 text-xl font-semibold">{{ item.title }}</p>
                <p class="item?.subtitle">{{ item.subtitle }}</p>
                <ul v-if="Array.isArray(item?.content)" class="pl-6">
                  <li
                    v-for="(subItem, subIndex) in item.content"
                    :key="subIndex"
                    class="list-disc not-last:mb-10px"
                  >
                    {{ subItem }}
                  </li>
                </ul>
                <p v-if="item?.subtitle2" class="mt-4">{{ item.subtitle2 }}</p>
                <template v-if="item?.subtitle2Temp?.temp">
                  <p class="mt-4">
                    <i18n-t
                      :keypath="`privacyPolicy.sections[${index}].content.content[${i}].subtitle2Temp.temp`"
                    >
                      <template #insert>
                        <a
                          :href="`mailto:${item.subtitle2Temp.insert}`"
                          class="font-semibold hover:underline"
                        >
                          {{ item.subtitle2Temp.insert }}
                        </a>
                      </template>
                    </i18n-t>
                  </p>
                </template>
              </li>
            </ul>
          </div>
        </template>
        <template v-else-if="[1, 2, 4, 6].includes(index)">
          <h2 class="mb-4 text-2xl font-semibold">{{ section.title }}</h2>
          <div>
            <p>{{ section.content.title }}</p>
            <ul v-if="section.content" class="pl-6 pt-20px">
              <li
                v-for="(item, i) in section.content.content"
                :key="i"
                class="list-disc not-last:mb-10px"
              >
                <p class="mb-2">
                  <span v-if="typeof item === 'string'">
                    {{ item }}
                  </span>
                  <template v-else>
                    <span class="font-semibold">{{ item.title }}</span>
                    <span>{{ item.content }}</span>
                  </template>
                </p>
              </li>
            </ul>
            <p v-if="section?.content?.subtitle2" class="mt-4">{{ section.content.subtitle2 }}</p>
            <p v-if="section?.content?.subtitle2Temp?.temp" class="mt-4">
              <i18n-t :keypath="`privacyPolicy.sections[${index}].content.subtitle2Temp.temp`">
                <template #insert>
                  <a
                    :href="`mailto:${section?.content?.subtitle2Temp.insert}`"
                    class="font-semibold hover:underline"
                  >
                    {{ section?.content?.subtitle2Temp.insert }}
                  </a>
                </template>
              </i18n-t>
            </p>
          </div>
        </template>
        <template v-else>
          <h2 class="mb-4 text-2xl font-semibold">{{ section.title }}</h2>
          <div>
            <p>{{ section.content.title }}</p>
          </div>
        </template>
      </section>

      <section class="mb-6">
        <h2 class="mb-4 text-2xl font-semibold">{{ $t('privacyPolicy.contactUs.title') }}</h2>
        <p class="mb-4">{{ $t('privacyPolicy.contactUs.content') }}</p>
        <p class="mb-4 font-semibold">{{ $t('privacyPolicy.contactUs.OlivegreenLLC') }}</p>
        <p>
          <span class="font-semibold">{{ $t('privacyPolicy.contactUs.邮箱') }}:</span>
          <a
            :href="`mailto:${$t('privacyPolicy.contactUs.email')}`"
            class="ml-10px hover:underline"
          >
            {{ $t('privacyPolicy.contactUs.email') }}
          </a>
        </p>
      </section>
    </div>
  </div>
</template>

<script setup>
  import { useI18n } from 'vue-i18n';
  import { computed, ref } from 'vue';
  const sections = computed(() => {
    const { messages, locale } = useI18n();
    return messages.value[locale.value].privacyPolicy.sections;
  });
</script>

<style lang="less"></style>
