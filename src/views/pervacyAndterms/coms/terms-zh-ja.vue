<template>
  <div>
    <div class="bg-#C0C1A4">
      <div class="mx-auto max-w-3xl px-4 py-8 container">
        <h1 class="mb-6 text-4xl font-semibold">{{ $t('termsOfUse.title') }}</h1>
        <h2 v-if="commonStore.currentLocale === 'ja'" class="mb-6 text-2xl font-semibold">
          {{ $t('termsOfUse.subtitleDate.title') }}
          <span class="text-18px font-400">
            {{ $t('termsOfUse.subtitleDate.date') }}
          </span>
        </h2>
        <h2 class="mb-6 text-2xl font-semibold">
          {{ $t('termsOfUse.subtitle') }}
        </h2>
        <p class="leading-6">{{ $t('termsOfUse.introduction') }}</p>
      </div>
    </div>
    <div class="mx-auto max-w-3xl px-4 py-8 container">
      <section class="mb-4" v-for="(section, index) in sections" :key="index">
        <h2 class="mb-4 text-2xl font-semibold">{{ section.title }}</h2>

        <p v-if="section.content" class="">{{ section.content }}</p>

        <div v-if="section.subsections" class="space-y-4">
          <div v-for="(subsection, subIndex) in section.subsections" :key="subIndex">
            <h3 class="mb-2 font-semibold">{{ subsection.subtitle }}</h3>
            <p class="">{{ subsection.content }}</p>
          </div>
        </div>
      </section>

      <section>
        <h2 class="mb-4 text-2xl font-semibold">{{ $t('termsOfUse.contactUs.title') }}</h2>
        <p class="flex">
          {{ $t('termsOfUse.contactUs.content') }}
          <a :href="`mailto:${$t('termsOfUse.contactUs.email')}`" class="hover:underline">
            {{ $t('termsOfUse.contactUs.email') }}
          </a>
        </p>
      </section>

      <section>
        <h2 class="mb-4 text-2xl font-semibold">{{ $t('termsOfUse.effectiveDate.title') }}</h2>
        <p class="">{{ $t('termsOfUse.effectiveDate.content') }}</p>
      </section>
    </div>
  </div>
</template>
<script setup>
  import { computed } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useCommonStore } from '@/stores/common';

  const commonStore = useCommonStore();
  const sections = computed(() => {
    const { messages, locale } = useI18n();
    return messages.value[locale.value].termsOfUse.sections;
  });
</script>
