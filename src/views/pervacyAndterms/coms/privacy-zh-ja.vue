<template>
  <selectLocale class="right-20px top-20px text-18px color-#374151 !fixed"></selectLocale>
  <div>
    <div class="bg-#C0C1A4">
      <div class="mx-auto max-w-3xl px-4 py-8 container">
        <h1 class="mb-6 text-4xl font-semibold">{{ $t('privacyPolicy.title') }}</h1>
        <h2 v-if="commonStore.currentLocale === 'zh'" class="mb-6 text-2xl font-semibold">
          {{ $t('privacyPolicy.title') }}
        </h2>
        <p class="leading-6">{{ $t('privacyPolicy.introduction') }}</p>
      </div>
    </div>
    <div class="mx-auto max-w-3xl px-4 py-8 container">
      <section v-for="(section, index) in sections" :key="index" class="mb-6">
        <h2 class="mb-4 text-2xl font-semibold">{{ section.title }}</h2>
        <div>
          <p>{{ section.content.intro }}</p>
          <ul v-if="section.content.infoTypes" class="pb-10px pl-6 pt-20px">
            <li v-for="(item, i) in section.content.infoTypes" :key="i" class="mb-10px list-disc">
              <span class="font-semibold">{{ item.title }}</span>
              <span>{{ item.content }}</span>
            </li>
          </ul>
          <p v-if="section.content.purpose">{{ section.content.purpose }}</p>
          <ul v-if="section.content.purposeList" class="pb-10px pl-6 pt-20px">
            <li v-for="(item, i) in section.content.purposeList" :key="i" class="mb-10px list-disc">
              {{ item }}
            </li>
          </ul>
          <ul v-if="section.content.measures" class="pb-10px pl-6 pt-20px">
            <li v-for="(item, i) in section.content.measures" :key="i" class="mb-10px list-disc">
              <template v-if="typeof item === 'string'">
                {{ item }}
              </template>
              <template v-else>
                <span class="font-semibold">{{ item.title }}</span>
                <span>{{ item.content }}</span>
              </template>
            </li>
          </ul>
          <p v-if="section.content.storage">{{ section.content.storage }}</p>
          <ul v-if="section.content.situations" class="pb-10px pl-6 pt-20px">
            <li v-for="(item, i) in section.content.situations" :key="i" class="mb-10px list-disc">
              <span>{{ item }}</span>
            </li>
          </ul>
          <ul v-if="section.content.rights" class="pb-10px pl-6 pt-20px">
            <li v-for="(item, i) in section.content.rights" :key="i" class="mb-10px list-disc">
              {{ item }}
            </li>
          </ul>
        </div>
      </section>

      <section class="mb-6">
        <h2 class="mb-4 text-2xl font-semibold">{{ $t('privacyPolicy.contactUs.title') }}</h2>
        <p class="mb-4">{{ $t('privacyPolicy.contactUs.content') }}</p>
        <p class="mb-4">
          <span class="font-semibold">{{ $t('privacyPolicy.contactUs.operatingCompany') }}:</span>
          <span class="ml-10px">
            {{ $t('privacyPolicy.contactUs.operatingCompanyContent') }}
          </span>
        </p>
        <p>
          <span class="font-semibold">{{ $t('privacyPolicy.contactUs.邮箱') }}:</span>
          <a
            :href="`mailto:${$t('privacyPolicy.contactUs.email')}`"
            class="ml-10px hover:underline"
          >
            {{ $t('privacyPolicy.contactUs.email') }}
          </a>
        </p>
      </section>

      <section>
        <h2 class="mb-4 text-2xl font-semibold">{{ $t('privacyPolicy.effectiveDate.title') }}</h2>
        <p>
          <i18n-t keypath="privacyPolicy.effectiveDate.contentTemp">
            <span class="font-semibold">{{ $t('privacyPolicy.effectiveDate.date') }}</span>
          </i18n-t>
        </p>
      </section>
    </div>
  </div>
</template>

<script setup>
  import { useI18n } from 'vue-i18n';
  import { computed } from 'vue';
  import { useCommonStore } from '@/stores/common';

  const commonStore = useCommonStore();
  const sections = computed(() => {
    const { messages, locale } = useI18n();
    return messages.value[locale.value].privacyPolicy.sections;
  });
</script>

<style lang="less"></style>
