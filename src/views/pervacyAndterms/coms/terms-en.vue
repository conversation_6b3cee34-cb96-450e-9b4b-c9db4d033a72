<template>
  <div>
    <div class="bg-#C0C1A4">
      <div class="mx-auto max-w-3xl px-4 py-8 container">
        <h1 class="mb-6 text-4xl font-semibold">{{ $t('termsOfUse.title') }}</h1>
        <h2 class="mb-6 text-2xl font-semibold">
          {{ $t('termsOfUse.subtitle') }}
          <span class="text-18px font-400">
            {{ $t('termsOfUse.subtitleInfo') }}
          </span>
        </h2>
        <p class="leading-6">{{ $t('termsOfUse.introduction') }}</p>
      </div>
    </div>
    <div class="mx-auto max-w-3xl px-4 py-8 container">
      <section class="mb-4" v-for="(section, index) in sections" :key="index">
        <h2 class="mb-4 text-2xl font-semibold">{{ section.title }}</h2>

        <!-- For sections with direct content -->
        <p v-if="section.content" class="">{{ section.content }}</p>

        <!-- For sections with subsections -->
        <div v-if="section.subsections" class="space-y-4">
          <div v-for="(subsection, subIndex) in section.subsections" :key="subIndex">
            <h3 class="mb-2 font-semibold" v-if="subsection.subtitle">{{ subsection.subtitle }}</h3>
            <p class="" v-if="subsection.content">{{ subsection.content }}</p>
            <ul class="mt-10px" v-if="subsection.contentArr?.length">
              <li
                v-for="(subContent, subContentIndex) in subsection.contentArr"
                :key="subContentIndex"
                class="mb-10px"
              >
                {{ subContent }}
              </li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section>
        <h2 class="mb-4 text-2xl font-semibold">{{ $t('termsOfUse.contactUs.title') }}</h2>
        <p class="mb-4 flex">
          {{ $t('termsOfUse.contactUs.content') }}
        </p>
        <p>
          <span class="font-semibold">{{ $t('termsOfUse.contactUs.邮箱') }}:</span>
          <a :href="`mailto:${$t('termsOfUse.contactUs.email')}`" class="ml-10px hover:underline">
            {{ $t('termsOfUse.contactUs.email') }}
          </a>
        </p>
      </section>
    </div>
  </div>
</template>
<script setup>
  import { computed } from 'vue';
  import { useI18n } from 'vue-i18n';

  const sections = computed(() => {
    const { messages, locale } = useI18n();
    return messages.value[locale.value].termsOfUse.sections;
  });
</script>
