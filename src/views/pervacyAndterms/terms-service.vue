<template>
  <selectLocale class="right-20px top-20px text-18px color-#374151 !fixed"></selectLocale>
  <terms-en v-if="commonStore.currentLocale === 'en'"></terms-en>
  <terms-zh-ja v-else></terms-zh-ja>
</template>

<script setup>
  import TermsZhJa from './coms/terms-zh-ja.vue';
  import TermsEn from './coms/terms-en.vue';
  import { useCommonStore } from '@/stores/common';

  const commonStore = useCommonStore();
</script>

<style lang="less"></style>
