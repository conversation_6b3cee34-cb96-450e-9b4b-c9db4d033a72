<template>
  <selectLocale class="right-20px top-20px text-18px color-#374151 !fixed"></selectLocale>
  <div>
    <div class="bg-#C0C1A4">
      <div class="mx-auto max-w-3xl px-4 py-8 container">
        <h1 class="text-4xl font-semibold">{{ $t('accountDeletion.title') }}</h1>
      </div>
    </div>
    <div class="mx-auto max-w-3xl px-4 pt-8 container">
      <div class="mb-6 flex gap-2">
        <div class="flex-shrink-0">1.</div>
        <div class="whitespace-break-spaces">
          {{ $t('accountDeletion.p1') }}
        </div>
      </div>
      <div class="mb-6 flex gap-2">
        <div class="flex-shrink-0">2.</div>
        <div class="whitespace-break-spaces">
          {{ $t('accountDeletion.p2') }}
        </div>
      </div>
      <div class="mb-6 flex gap-2">
        <div class="flex-shrink-0">3.</div>
        <div class="whitespace-break-spaces">
          {{ $t('accountDeletion.p3') }}
        </div>
      </div>
    </div>
  </div>
  <div class="mx-auto max-w-3xl px-4 container">
    <el-checkbox-group v-model="checkList">
      <!-- text-color="#221C02" fill="#221C02"  -->
      <el-checkbox :label="$t('accountDeletion.tip24')" />
    </el-checkbox-group>
    <div class="text-center">
      <el-button
        class="mt-2"
        color="#221C02"
        :disabled="checkList.length === 0"
        type="primary"
        @click="openDialog"
      >
        {{ $t('accountDeletion.confirmDeletion') }}
      </el-button>
    </div>
  </div>
  <el-dialog
    v-model="visible"
    :title="$t('accountDeletion.authentication')"
    width="300px"
    :show-close="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    center
  >
    <el-form :model="form" label-position="top">
      <el-form-item>
        <el-input v-model="form.username" :placeholder="$t('用户名')" />
      </el-form-item>
      <el-form-item>
        <div class="flex gap-2">
          <el-input v-model="form.code" :placeholder="$t('验证码')" class="flex-1" />
          <el-button color="#221C02" :disabled="disabledSend" @click="sendCode" type="primary">
            {{ countdown > 0 ? `${countdown}${$t('秒')}` : $t('发送验证码') }}
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="closeDialog" color="#64604E">{{ $t('取消') }}</el-button>
      <el-button type="danger" @click="confirmLogout">
        {{ $t('accountDeletion.confirmDeletion') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import { useCommonStore } from '@/stores/common';
  import { accountDeletionApi, accountDeletionCodeApi } from '@/service/APIcustomer';
  import i18n from '@/i18n';

  const commonStore = useCommonStore();
  const checkList = ref([]);
  import { ElMessage } from 'element-plus';

  const visible = ref(false);

  const form = ref({
    username: '',
    code: '',
  });

  const isSending = ref(false);
  const countdown = ref(0);

  const disabledSend = computed(() => {
    return isSending.value || countdown.value > 0;
  });
  let timer = null;

  const openDialog = () => {
    visible.value = true;
  };

  const closeDialog = () => {
    visible.value = false;
    clearInterval(timer);
    countdown.value = 0;
    isSending.value = false;
    form.value.username = '';
    form.value.code = '';
  };

  const sendCode = async () => {
    if (!form.value.username) {
      ElMessage.warning(i18n.t('请输入用户名'));
      return;
    }

    try {
      isSending.value = true;
      const usernameType = form.value.username.includes('@') ? 2 : 0; // 判断是邮箱还是手机号
      console.log('发送验证码请求参数:', {
        username: form.value.username,
        usernameType,
      });

      const response = await accountDeletionCodeApi({
        username: form.value.username,
        usernameType,
      });

      console.log('验证码发送响应:', response);
      ElMessage.success(i18n.t('验证码已发送'));

      countdown.value = 60;
      timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          isSending.value = false;
          clearInterval(timer);
        }
      }, 1000);
    } catch (e) {
      console.error('发送验证码失败:', e);
      isSending.value = false;
      ElMessage.error(
        i18n.t('发送失败') + (e.response?.data?.message ? `: ${e.response.data.message}` : ''),
      );
    }
  };

  const confirmLogout = async () => {
    if (!form.value.username || !form.value.code) {
      ElMessage.warning(i18n.t('请输入完整信息'));
      return;
    }

    try {
      console.log('注销账号请求参数:', {
        username: form.value.username,
        verificationCode: form.value.code,
      });

      const response = await accountDeletionApi({
        username: form.value.username,
        password: form.value.code,
      });

      console.log('注销账号响应:', response);
      ElMessage.success(i18n.t('注销成功'));
      closeDialog();
    } catch (err) {
      console.error('注销账号失败:', err);
      ElMessage.error(
        i18n.t('注销失败') + (err.response?.data?.message ? `: ${err.response.data.message}` : ''),
      );
    }
  };
</script>

<style lang="less" scoped>
  :deep {
    .el-checkbox.is-checked {
      color: #221c02;
      .el-checkbox__label {
        color: #221c02;
      }
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #221c02;
      border-color: #221c02;
    }
  }
</style>
