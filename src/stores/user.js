import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getUserInfoApi } from '@/service/login/login';
export const useUserStore = defineStore('user', () => {
  const userInfo = ref(null);
  const token = ref('');
  const customerId = ref('');
  const getUserInfo = async (code) => {
    // 请求用户信息
    if (!token.value) return;
    const result = await getUserInfoApi();
    userInfo.value = result.body;
  };
  // 设置token
  const setStateToken = (newToken) => {
    token.value = newToken;
  };
  // 设置customerId
  const setStateCustomerId = (newCustomerId) => {
    customerId.value = newCustomerId;
  };

  return {
    userInfo,
    getUserInfo,
    token,
    setStateToken,
    customerId,
    setStateCustomerId,
  };
});
