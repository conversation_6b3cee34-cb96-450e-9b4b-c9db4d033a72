/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution');

module.exports = {
  root: true,
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-prettier/skip-formatting',
    '@unocss',
  ],
  parserOptions: {
    ecmaVersion: 'latest',
  },
  globals: {
    process: true,
  },
  rules: {
    // 0 = off, 1 = warn, 2 = error
    'prefer-template': 'error', // 使用模板字符串
    'prettier/prettier': 'warn', // Prettier 格式化规则警告
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // 生产环境禁用 console
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // 生产环境禁用 debugger
    'no-unused-vars': 'off', // 允许未使用的变量
    'no-var': 'warn', // 建议使用 let 或 const 而不是 var
    'no-useless-escape': 'off', // 允许不必要的转义字符
    'vue/no-unused-vars': 'warn', // Vue 文件中未使用的变量给出警告
    'no-prototype-builtins': 'off', // 允许直接调用 Object.prototype 的方法
    'vue/no-unused-components': 'off', // 允许未使用的组件
    'vue/no-side-effects-in-computed-properties': 'warn', // 计算属性中的副作用给出警告
    'vue/no-mutating-props': 'off', // 允许修改 props
    'vue/multi-word-component-names': 'off', // 允许单个单词的组件名
    'comma-dangle': [2, 'always-multiline'], // 多行对象字面量、数组等的最后一项后要加逗号
    'vue/script-setup-uses-vars': 'error', // 确保 <script setup> 中声明的变量被正确识别为已使用
    'vue/no-deprecated-slot-attribute': 'error', // 禁止使用已废弃的 slot 属性
    'vue/no-deprecated-slot-scope-attribute': 'error', // 禁止使用已废弃的 slot-scope 属性
    'vue/no-deprecated-scope-attribute': 'error', // 禁止使用已废弃的 scope 属性
    'vue/require-explicit-emits': 'warn', // 要求显式声明 emits，但仅给出警告
    // 强制要求在单行注释 // 和多行注释 /* 之后有一个空格
    'spaced-comment': [
      'error',
      'always',
      {
        line: {
          markers: ['/'],
          exceptions: ['-', '+'],
        },
        block: {
          markers: ['!'],
          exceptions: ['*'],
          balanced: true,
        },
      },
    ],
  },
};
