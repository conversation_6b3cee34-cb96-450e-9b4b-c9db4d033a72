/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BgContainer: typeof import('./src/components/bg-container.vue')['default']
    ConcatForm: typeof import('./src/components/concat-form.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTree: typeof import('element-plus/es')['ElTree']
    FixedSidebar: typeof import('./src/components/home/<USER>')['default']
    PasswordInputWithStrength: typeof import('./src/components/passwordInputWithStrength.vue')['default']
    PasswordWithStrength: typeof import('./src/components/passwordWithStrength.vue')['default']
    QmcFooter: typeof import('./src/components/qmc-footer.vue')['default']
    QmcHeader: typeof import('./src/components/qmc-header.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectLocale: typeof import('./src/components/select-locale/src/select-locale.vue')['default']
    SignupBtn: typeof import('./src/components/home/<USER>')['default']
    SwiperBox: typeof import('./src/components/home/<USER>')['default']
    VideoThumbnailList: typeof import('./src/components/home/<USER>')['default']
  }
}
