import { fileURLToPath, URL } from 'node:url';
import UnoCSS from 'unocss/vite';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  console.log('mode------', mode);
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  const modeList = mode.split('.');
  const baseEnv = modeList[0] ? loadEnv(modeList[0], process.cwd()) : {};
  const env = loadEnv(mode, process.cwd());

  // 将公共配置与国家配置合并
  const finalEnv = { ...baseEnv, ...env };
  // 将合并后的配置写入到 process.env 中
  for (const k in finalEnv) {
    process.env[k] = finalEnv[k];
  }
  // console.log('outDir---', finalEnv.VITE_APP_OUTPUTDIR)

  return {
    plugins: [
      vue(),
      UnoCSS(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ],
    base: './',
    build: {
      outDir: finalEnv.VITE_APP_OUTPUTDIR,
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          // TODO 注意：additionalData 不会触发热更新
          additionalData: `@import './src/assets/fix-element.less';`,
        },
      },
    },
    // vite 配置
    define: {
      __APP_ENV__: JSON.stringify(finalEnv),
    },
    // 配置前端服务地址和端口
    server: {
      // 配置host:'0.0.0.0'局域网内其他设备可以访问本设备的ip地址
      // host: '0.0.0.0',
      port: 4000,
      // 设置反向代理，跨域
      // proxy: {
      //   '/api': {
      //     // 后台地址
      //     target: 'http://127.0.0.1:3000/',
      //     changeOrigin: true,
      //     rewrite: (path) => path.replace(/^\/api/, '')
      //   }
      // }
    },
  };
});
