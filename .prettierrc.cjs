module.exports = {
  // 单行最大长度
  printWidth: 100,
  // 设置编辑器每一个水平缩进的空格数
  tabWidth: 2,
  // 在句尾添加分号
  semi: true,
  // 使用单引号
  singleQuote: true,
  // 在任何可能的多行中输入尾逗号。
  trailingComma: 'all',
  // 在对象字面量声明所使用的的花括号后（{）和前（}）输出空格
  bracketSpacing: true,
  // 为单行箭头函数的参数添加圆括号。
  arrowParens: 'always',
  // 在 Windows 和 Mac 系统上保持一致的行尾符
  endOfLine: 'lf',
  // 是否缩进Vue 文件中的代码<script>和<style>标签
  vueIndentScriptAndStyle: true,
  // HTML 空白敏感度
  htmlWhitespaceSensitivity: 'ignore',
};
