import {
  presetIcons,
  defineConfig,
  presetAttributify,
  presetUno,
  transformerVariantGroup,
} from 'unocss';

import transformerDirectives from '@unocss/transformer-directives'; // 支持@apply
export default defineConfig({
  theme: {
    colors: {
      primary: 'var(--color-base)',
      'cannon-black': 'var(--cannon-black)',
      'pale-olive': 'var(--pale-olive)',
      crete: 'var(--crete)',
      indigo: 'var(--pigment-indigo)',
      'indigo-light': 'var(--pigment-indigo-light)',
      'indigo-dark': 'var(--pigment-indigo-dark)',
      white: 'var(--white)',
    },
  },
  extendTheme: (theme) => {
    console.log('theme---', theme.breakpoints);
    return {
      ...theme,
      breakpoints: {
        ...theme.breakpoints, // 继承所有默认断点
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        1400: '1400px', // 新增 1400px 断点
        '2xl': '1536px',
      },
    };
  },
  presets: [
    presetAttributify({
      /* preset options */
    }),
    presetUno(),
    presetIcons({
      // 加载 Fluent MDL2 图标集
      collections: {
        // 使用动态导入加载图标集
        fluent: () => import('@iconify-json/fluent-mdl2/icons.json').then((i) => i.default),
      },
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  shortcuts: {
    // 全局简易变量在这里配置
    // 'btn-green': 'text-white bg-green-500 hover:bg-green-700'
  },
});
