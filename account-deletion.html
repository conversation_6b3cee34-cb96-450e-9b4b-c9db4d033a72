<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Deletion Notice</title>
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .container {
            max-width: 768px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .bg-custom {
            background-color: #C0C1A4;
        }
        
        .btn-primary {
            background-color: #221C02;
            border-color: #221C02;
        }
        
        .btn-primary:hover {
            background-color: #1a1501;
            border-color: #1a1501;
        }
        
        .btn-primary:disabled {
            background-color: #9ca3af;
            border-color: #9ca3af;
        }
        
        .btn-cancel {
            background-color: #64604E;
            border-color: #64604E;
        }
        
        .checkbox-custom:checked {
            background-color: #221C02;
            border-color: #221C02;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            width: 300px;
            max-width: 90%;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn:disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }
        
        .flex-gap {
            display: flex;
            gap: 8px;
        }
        
        .flex-1 {
            flex: 1;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mt-2 {
            margin-top: 8px;
        }
        
        .mb-6 {
            margin-bottom: 24px;
        }
        
        .whitespace-pre-line {
            white-space: pre-line;
        }
        
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 2000;
            display: none;
        }
        
        .message.success {
            background-color: #f0f9ff;
            color: #0369a1;
            border: 1px solid #7dd3fc;
        }
        
        .message.error {
            background-color: #fef2f2;
            color: #dc2626;
            border: 1px solid #fca5a5;
        }
        
        .message.warning {
            background-color: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        .message.show {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Message container -->
    <div id="messageContainer" class="message"></div>
    
    <!-- Main content -->
    <div>
        <!-- Header -->
        <div class="bg-custom">
            <div class="container py-8">
                <h1 class="text-4xl font-semibold">Account Deletion Notice</h1>
            </div>
        </div>
        
        <!-- Content -->
        <div class="container pt-8">
            <div class="mb-6 flex gap-2">
                <div class="flex-shrink-0">1.</div>
                <div class="whitespace-pre-line">
Irreversible Deletion
Once your deletion request is submitted and approved, your account will be permanently deleted. This includes, but is not limited to, your login credentials and personal profile information. After deletion, you will no longer be able to log in or use EduLoop.
                </div>
            </div>
            <div class="mb-6 flex gap-2">
                <div class="flex-shrink-0">2.</div>
                <div class="whitespace-pre-line">
Partial Data Retention for Compliance
Account deletion only removes data directly associated with your user account. If you have previously purchased courses, created student profiles, or generated orders, some data may be temporarily retained for compliance with third-party course providers. To request full deletion of all related data (e.g., student profiles, orders, purchased course records), please contact the respective course provider and follow their data deletion procedure.
                </div>
            </div>
            <div class="mb-6 flex gap-2">
                <div class="flex-shrink-0">3.</div>
                <div class="whitespace-pre-line">
Final and Non-reversible
After deletion is completed, this action cannot be undone. Please make sure you have backed up or processed any important data before proceeding.
                </div>
            </div>
        </div>
        
        <!-- Checkbox and button -->
        <div class="container">
            <div class="mb-4">
                <label class="flex items-center gap-2">
                    <input type="checkbox" id="agreementCheckbox" class="checkbox-custom">
                    <span>I agree to the above terms and authorize account deletion.</span>
                </label>
            </div>
            <div class="text-center">
                <button id="confirmButton" class="btn btn-primary mt-2" disabled onclick="openDialog()">
                    Confirm Deletion
                </button>
            </div>
        </div>
    </div>
    
    <!-- Modal -->
    <div id="authModal" class="modal">
        <div class="modal-content">
            <h3 class="text-lg font-semibold mb-4">Authentication</h3>
            <form id="authForm">
                <div class="form-group">
                    <input type="text" id="username" class="form-input" placeholder="Username" required>
                </div>
                <div class="form-group">
                    <div class="flex-gap">
                        <input type="text" id="verificationCode" class="form-input flex-1" placeholder="Verification Code" required>
                        <button type="button" id="sendCodeButton" class="btn btn-primary" onclick="sendCode()">
                            <span id="sendCodeText">Send Code</span>
                        </button>
                    </div>
                </div>
                <div class="flex-gap mt-4">
                    <button type="button" class="btn btn-cancel flex-1" onclick="closeDialog()">Cancel</button>
                    <button type="button" class="btn btn-primary flex-1" style="background-color: #dc2626; border-color: #dc2626;" onclick="confirmDeletion()">Confirm Deletion</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global variables
        let countdown = 0;
        let isSending = false;
        let timer = null;
        
        // API base URL - you may need to adjust this
        const API_BASE_URL = 'https://your-api-domain.com'; // Replace with actual API URL
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            const checkbox = document.getElementById('agreementCheckbox');
            const confirmButton = document.getElementById('confirmButton');
            
            checkbox.addEventListener('change', function() {
                confirmButton.disabled = !this.checked;
            });
        });
        
        // Show message
        function showMessage(text, type = 'success') {
            const messageContainer = document.getElementById('messageContainer');
            messageContainer.textContent = text;
            messageContainer.className = `message ${type} show`;
            
            setTimeout(() => {
                messageContainer.classList.remove('show');
            }, 3000);
        }
        
        // Open dialog
        function openDialog() {
            document.getElementById('authModal').classList.add('show');
        }
        
        // Close dialog
        function closeDialog() {
            document.getElementById('authModal').classList.remove('show');
            clearInterval(timer);
            countdown = 0;
            isSending = false;
            document.getElementById('username').value = '';
            document.getElementById('verificationCode').value = '';
            updateSendCodeButton();
        }
        
        // Update send code button
        function updateSendCodeButton() {
            const button = document.getElementById('sendCodeButton');
            const text = document.getElementById('sendCodeText');
            
            if (countdown > 0) {
                button.disabled = true;
                text.textContent = `${countdown} Seconds`;
            } else if (isSending) {
                button.disabled = true;
                text.textContent = 'Sending...';
            } else {
                button.disabled = false;
                text.textContent = 'Send Code';
            }
        }
        
        // Send verification code
        async function sendCode() {
            const username = document.getElementById('username').value.trim();
            
            if (!username) {
                showMessage('Please enter your username', 'warning');
                return;
            }
            
            try {
                isSending = true;
                updateSendCodeButton();
                
                const usernameType = username.includes('@') ? 2 : 0; // 2 for email, 0 for phone
                
                const response = await fetch(`${API_BASE_URL}/olive/user/api/a/delete/attempt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: {
                            username: username,
                            usernameType: usernameType
                        }
                    })
                });
                
                if (response.ok) {
                    showMessage('Verification code sent', 'success');
                    
                    // Start countdown
                    countdown = 60;
                    timer = setInterval(() => {
                        countdown--;
                        updateSendCodeButton();
                        
                        if (countdown <= 0) {
                            isSending = false;
                            clearInterval(timer);
                            updateSendCodeButton();
                        }
                    }, 1000);
                } else {
                    throw new Error('Failed to send verification code');
                }
            } catch (error) {
                console.error('Send code error:', error);
                isSending = false;
                updateSendCodeButton();
                showMessage('Failed to send verification code', 'error');
            }
        }
        
        // Confirm deletion
        async function confirmDeletion() {
            const username = document.getElementById('username').value.trim();
            const code = document.getElementById('verificationCode').value.trim();
            
            if (!username || !code) {
                showMessage('Please enter complete information', 'warning');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/olive/user/api/a/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: {
                            username: username,
                            password: code
                        }
                    })
                });
                
                if (response.ok) {
                    showMessage('Account deleted successfully', 'success');
                    closeDialog();
                } else {
                    throw new Error('Failed to delete account');
                }
            } catch (error) {
                console.error('Delete account error:', error);
                showMessage('Failed to delete account', 'error');
            }
        }
    </script>
</body>
</html>
