{
  // Place your Edurp-New 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  "tt": {
    // "scope": "vue,html",//vue 文件和 html
    "prefix": "tt", // 触发方式
    "body": ["{{\\$t('global.$1')}}"],
    "description": "vue $t",
  },
  "tg": {
    // "scope": "vue,html",//vue 文件和 html
    "prefix": "tg", // 触发方式
    "body": ["\\$t('global.$1')"],
    "description": "vue $t",
  },
  "tp": {
    // "scope": "vue,html",//vue 文件和 html
    "prefix": "tp", // 触发方式
    "body": ["\\$t('placeholder.$1')"],
    "description": "vue $t",
  },
  "jstt": {
    "scope": "javascript",
    "prefix": "jg", // 触发方式
    "body": ["this.\\$t('global.$1')"],
    "description": "js $t",
  },
  "jstp": {
    "scope": "javascript", //vue 文件和 html
    "prefix": "jp", // 触发方式
    "body": ["this.\\$t('placeholder.$1')"],
    "description": "js $t",
  },
  "windowi18ntglobal": {
    "scope": "javascript",
    "prefix": "wg", // 触发方式
    "body": ["window.i18n.t('global.$1')"],
    "description": "global",
  },
  "windowi18ntplaceholder": {
    "scope": "javascript",
    "prefix": "wp", // 触发方式
    "body": ["window.i18n.t('placeholder.$1')"],
    "description": "placeholder",
  },
}
